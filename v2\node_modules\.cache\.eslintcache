[{"D:\\Via\\New folder\\v2\\src\\index.js": "1", "D:\\Via\\New folder\\v2\\src\\App.js": "2", "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js": "3", "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx": "4", "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx": "5", "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx": "6", "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx": "7", "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx": "8", "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx": "9", "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx": "10", "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx": "12", "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx": "13", "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx": "14", "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx": "15", "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx": "16", "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx": "17", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx": "18", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx": "19", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx": "20", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx": "21", "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx": "22", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "23", "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js": "24", "D:\\Via\\New folder\\v2\\src\\services\\api.js": "25", "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js": "26", "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js": "27", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx": "28", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "29", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx": "30", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx": "31", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "32", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx": "33", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx": "34", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx": "35", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx": "36", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx": "37", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx": "38", "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js": "39", "D:\\Via\\New folder\\v2\\src\\components\\EmbedCodeGenerator.jsx": "40", "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientSettings.jsx": "41"}, {"size": 653, "mtime": 1749196504388, "results": "42", "hashOfConfig": "43"}, {"size": 4865, "mtime": 1749206413124, "results": "44", "hashOfConfig": "43"}, {"size": 362, "mtime": 1746042995461, "results": "45", "hashOfConfig": "43"}, {"size": 7675, "mtime": 1749196504239, "results": "46", "hashOfConfig": "43"}, {"size": 19373, "mtime": 1748874413890, "results": "47", "hashOfConfig": "43"}, {"size": 19487, "mtime": 1748874429932, "results": "48", "hashOfConfig": "43"}, {"size": 17189, "mtime": 1748864169684, "results": "49", "hashOfConfig": "43"}, {"size": 41162, "mtime": 1748975550119, "results": "50", "hashOfConfig": "43"}, {"size": 60392, "mtime": 1748605437244, "results": "51", "hashOfConfig": "43"}, {"size": 16901, "mtime": 1748864143496, "results": "52", "hashOfConfig": "43"}, {"size": 6108, "mtime": 1746344994148, "results": "53", "hashOfConfig": "43"}, {"size": 16294, "mtime": 1748293996467, "results": "54", "hashOfConfig": "43"}, {"size": 25359, "mtime": 1748867397685, "results": "55", "hashOfConfig": "43"}, {"size": 7100, "mtime": 1748867056681, "results": "56", "hashOfConfig": "43"}, {"size": 11616, "mtime": 1746342271795, "results": "57", "hashOfConfig": "43"}, {"size": 6479, "mtime": 1748866960444, "results": "58", "hashOfConfig": "43"}, {"size": 14287, "mtime": 1749152911428, "results": "59", "hashOfConfig": "43"}, {"size": 20074, "mtime": 1749203894531, "results": "60", "hashOfConfig": "43"}, {"size": 14152, "mtime": 1749206260914, "results": "61", "hashOfConfig": "43"}, {"size": 17374, "mtime": 1749203517435, "results": "62", "hashOfConfig": "43"}, {"size": 12177, "mtime": 1749206358128, "results": "63", "hashOfConfig": "43"}, {"size": 17624, "mtime": 1749205884454, "results": "64", "hashOfConfig": "43"}, {"size": 3728, "mtime": 1749205954145, "results": "65", "hashOfConfig": "43"}, {"size": 8174, "mtime": 1748283061371, "results": "66", "hashOfConfig": "43"}, {"size": 1190, "mtime": 1748291506520, "results": "67", "hashOfConfig": "43"}, {"size": 10601, "mtime": 1748277235110, "results": "68", "hashOfConfig": "43"}, {"size": 4297, "mtime": 1748283089634, "results": "69", "hashOfConfig": "43"}, {"size": 6733, "mtime": 1749196502414, "results": "70", "hashOfConfig": "43"}, {"size": 6636, "mtime": 1749198338414, "results": "71", "hashOfConfig": "43"}, {"size": 8425, "mtime": 1749206130605, "results": "72", "hashOfConfig": "43"}, {"size": 5620, "mtime": 1749196502795, "results": "73", "hashOfConfig": "43"}, {"size": 6238, "mtime": 1749196501461, "results": "74", "hashOfConfig": "43"}, {"size": 6156, "mtime": 1749196502910, "results": "75", "hashOfConfig": "43"}, {"size": 8877, "mtime": 1749150209342, "results": "76", "hashOfConfig": "43"}, {"size": 7144, "mtime": 1749144357974, "results": "77", "hashOfConfig": "43"}, {"size": 5658, "mtime": 1749196503295, "results": "78", "hashOfConfig": "43"}, {"size": 8337, "mtime": 1749196503594, "results": "79", "hashOfConfig": "43"}, {"size": 8526, "mtime": 1749205011761, "results": "80", "hashOfConfig": "43"}, {"size": 5096, "mtime": 1748291546769, "results": "81", "hashOfConfig": "43"}, {"size": 9697, "mtime": 1749203944731, "results": "82", "hashOfConfig": "43"}, {"size": 19275, "mtime": 1749205741948, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gdfhta", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Via\\New folder\\v2\\src\\index.js", [], [], "D:\\Via\\New folder\\v2\\src\\App.js", [], [], "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx", ["207"], [], "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx", ["208"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx", ["209"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx", ["210", "211", "212", "213", "214", "215", "216"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx", ["217", "218"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx", ["219", "220", "221"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", ["222", "223"], [], "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\New folder\\v2\\src\\services\\api.js", [], [], "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js", [], [], "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["224", "225", "226"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["227"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx", ["228"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx", ["229"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["230"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx", ["231"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx", ["232"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js", [], [], "D:\\Via\\New folder\\v2\\src\\components\\EmbedCodeGenerator.jsx", ["233", "234", "235"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientSettings.jsx", ["236", "237"], [], {"ruleId": "238", "severity": 1, "message": "239", "line": 203, "column": 6, "nodeType": "240", "endLine": 203, "endColumn": 16, "suggestions": "241"}, {"ruleId": "242", "severity": 1, "message": "243", "line": 37, "column": 9, "nodeType": "244", "messageId": "245", "endLine": 37, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "246", "line": 5, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 16}, {"ruleId": "242", "severity": 1, "message": "247", "line": 1, "column": 27, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 36}, {"ruleId": "242", "severity": 1, "message": "248", "line": 5, "column": 27, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 35}, {"ruleId": "242", "severity": 1, "message": "249", "line": 5, "column": 37, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 40}, {"ruleId": "242", "severity": 1, "message": "250", "line": 6, "column": 48, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 53}, {"ruleId": "242", "severity": 1, "message": "251", "line": 6, "column": 62, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 72}, {"ruleId": "242", "severity": 1, "message": "252", "line": 6, "column": 74, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 81}, {"ruleId": "242", "severity": 1, "message": "253", "line": 12, "column": 25, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 41}, {"ruleId": "242", "severity": 1, "message": "254", "line": 4, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 4, "endColumn": 16}, {"ruleId": "242", "severity": 1, "message": "255", "line": 26, "column": 9, "nodeType": "244", "messageId": "245", "endLine": 26, "endColumn": 26}, {"ruleId": "242", "severity": 1, "message": "247", "line": 1, "column": 27, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 36}, {"ruleId": "242", "severity": 1, "message": "256", "line": 7, "column": 61, "nodeType": "244", "messageId": "245", "endLine": 7, "endColumn": 66}, {"ruleId": "242", "severity": 1, "message": "257", "line": 14, "column": 22, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 35}, {"ruleId": "242", "severity": 1, "message": "247", "line": 1, "column": 27, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 36}, {"ruleId": "242", "severity": 1, "message": "258", "line": 1, "column": 38, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 44}, {"ruleId": "242", "severity": 1, "message": "248", "line": 5, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "249", "line": 6, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 6}, {"ruleId": "242", "severity": 1, "message": "259", "line": 14, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "259", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "259", "line": 14, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "259", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "259", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "259", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "260", "line": 1, "column": 17, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 25}, {"ruleId": "242", "severity": 1, "message": "261", "line": 3, "column": 23, "nodeType": "244", "messageId": "245", "endLine": 3, "endColumn": 27}, {"ruleId": "262", "severity": 1, "message": "263", "line": 197, "column": 31, "nodeType": "264", "endLine": 197, "endColumn": 65}, {"ruleId": "262", "severity": 1, "message": "263", "line": 198, "column": 21, "nodeType": "264", "endLine": 198, "endColumn": 60}, {"ruleId": "242", "severity": 1, "message": "254", "line": 4, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 4, "endColumn": 16}, {"ruleId": "242", "severity": 1, "message": "256", "line": 5, "column": 36, "nodeType": "244", "messageId": "245", "endLine": 5, "endColumn": 41}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'activeCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["265"], "no-unused-vars", "'isClient' is assigned a value but never used.", "Identifier", "unusedVar", "'Search' is defined but never used.", "'useEffect' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Clock' is defined but never used.", "'Smartphone' is defined but never used.", "'Monitor' is defined but never used.", "'setDashboardData' is assigned a value but never used.", "'motion' is defined but never used.", "'handleInputChange' is assigned a value but never used.", "'Globe' is defined but never used.", "'setClientData' is assigned a value but never used.", "'useRef' is defined but never used.", "'Legend' is defined but never used.", "'useState' is defined but never used.", "'Code' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"desc": "266", "fix": "267"}, "Update the dependencies array to be: [activeCategory, location]", {"range": "268", "text": "269"}, [9369, 9379], "[activeCategory, location]"]