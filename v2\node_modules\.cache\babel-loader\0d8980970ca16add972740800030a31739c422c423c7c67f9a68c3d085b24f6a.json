{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\components\\\\client\\\\ClientNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientNavbar = ({\n  toggleSidebar,\n  collapsed\n}) => {\n  _s();\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const user = JSON.parse(localStorage.getItem('user'));\n  const notifications = [{\n    id: 1,\n    title: 'Try-on Session Completed',\n    message: 'A user completed a virtual try-on session',\n    time: '5 minutes ago',\n    type: 'tryon'\n  }, {\n    id: 2,\n    title: 'High Engagement Alert',\n    message: 'Your product \"Watch Model A\" is trending',\n    time: '1 hour ago',\n    type: 'engagement'\n  }, {\n    id: 3,\n    title: 'Weekly Report Ready',\n    message: 'Your weekly analytics report is available',\n    time: '2 hours ago',\n    type: 'report'\n  }];\n\n  // Set left margin based on collapsed state\n  const leftMargin = collapsed ? 'md:left-[80px]' : 'md:left-[280px]';\n  useEffect(() => {\n    function handleClickOutside(event) {\n      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n        setIsNotificationsOpen(false);\n      }\n      if (profileRef.current && !profileRef.current.contains(event.target)) {\n        setIsProfileOpen(false);\n      }\n    }\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  const handleSearch = e => {\n    e.preventDefault();\n    // Implement search functionality\n    console.log('Searching for:', searchQuery);\n  };\n  const menuItems = [{\n    title: 'Dashboard',\n    path: '/client/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Virtual Try-On',\n    path: '/virtual-try-on',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(motion.nav, {\n    initial: {\n      y: -100\n    },\n    animate: {\n      y: 0\n    },\n    className: \"fixed top-0 right-0 left-0 bg-white border-b border-gray-200 z-40\",\n    style: {\n      marginLeft: collapsed ? 80 : 280\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 h-16 flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleSidebar,\n          className: \"p-2 rounded-lg hover:bg-gray-100 text-gray-600 md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              placeholder: \"Search...\",\n              className: \"w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: notificationsRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsNotificationsOpen(!isNotificationsOpen),\n            className: \"p-2 rounded-lg hover:bg-gray-100 text-gray-600 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), isNotificationsOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-2 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-96 overflow-y-auto\",\n              children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-2 h-2 rounded-full mt-2 ${notification.type === 'success' ? 'bg-green-500' : notification.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: notification.time\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 23\n                }, this)\n              }, notification.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-2 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"text-sm text-[#2D8C88] hover:text-[#2D8C88]/80 font-medium\",\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: profileRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsProfileOpen(!isProfileOpen),\n            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'Client User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: user === null || user === void 0 ? void 0 : user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: `h-5 w-5 text-gray-400 transition-transform duration-200 ${isProfileOpen ? 'rotate-180' : ''}`,\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M19 9l-7 7-7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), isProfileOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-2 border-b border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Signed in as\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 truncate\",\n                children: user === null || user === void 0 ? void 0 : user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/client/settings\",\n                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  localStorage.clear();\n                  window.location.href = '/login';\n                },\n                className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100\",\n                children: \"Sign out\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-2 border-t border-gray-200 overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${location.pathname === item.path ? 'bg-[#2D8C88]/10 text-[#2D8C88]' : 'text-gray-600 hover:bg-gray-100'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex-shrink-0\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientNavbar, \"tqGzBWX2Vl/4vfOOou9GEEmeCjU=\");\n_c = ClientNavbar;\nexport default ClientNavbar;\nvar _c;\n$RefreshReg$(_c, \"ClientNavbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "motion", "jsxDEV", "_jsxDEV", "ClientNavbar", "toggleSidebar", "collapsed", "_s", "isNotificationsOpen", "setIsNotificationsOpen", "isProfileOpen", "setIsProfileOpen", "user", "JSON", "parse", "localStorage", "getItem", "notifications", "id", "title", "message", "time", "type", "leftMargin", "useEffect", "handleClickOutside", "event", "notificationsRef", "current", "contains", "target", "profileRef", "document", "addEventListener", "removeEventListener", "handleSearch", "e", "preventDefault", "console", "log", "searchQuery", "menuItems", "path", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nav", "initial", "y", "animate", "style", "marginLeft", "onClick", "onSubmit", "value", "onChange", "setSearch<PERSON>uery", "placeholder", "ref", "map", "notification", "name", "email", "to", "clear", "window", "location", "href", "item", "pathname", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/components/client/ClientNavbar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\nconst ClientNavbar = ({ toggleSidebar, collapsed }) => {\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const user = JSON.parse(localStorage.getItem('user'));\n\n  const notifications = [\n    {\n      id: 1,\n      title: 'Try-on Session Completed',\n      message: 'A user completed a virtual try-on session',\n      time: '5 minutes ago',\n      type: 'tryon'\n    },\n    {\n      id: 2,\n      title: 'High Engagement Alert',\n      message: 'Your product \"Watch Model A\" is trending',\n      time: '1 hour ago',\n      type: 'engagement'\n    },\n    {\n      id: 3,\n      title: 'Weekly Report Ready',\n      message: 'Your weekly analytics report is available',\n      time: '2 hours ago',\n      type: 'report'\n    }\n  ];\n\n  // Set left margin based on collapsed state\n  const leftMargin = collapsed ? 'md:left-[80px]' : 'md:left-[280px]';\n\n  useEffect(() => {\n    function handleClickOutside(event) {\n      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n        setIsNotificationsOpen(false);\n      }\n      if (profileRef.current && !profileRef.current.contains(event.target)) {\n        setIsProfileOpen(false);\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    // Implement search functionality\n    console.log('Searching for:', searchQuery);\n  };\n\n  const menuItems = [\n    {\n      title: 'Dashboard',\n      path: '/client/dashboard',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Virtual Try-On',\n      path: '/virtual-try-on',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n    }\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className=\"fixed top-0 right-0 left-0 bg-white border-b border-gray-200 z-40\"\n      style={{ marginLeft: collapsed ? 80 : 280 }}\n    >\n      <div className=\"px-4 h-16 flex items-center justify-between\">\n        {/* Left side - Mobile menu button and Search */}\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={toggleSidebar}\n            className=\"p-2 rounded-lg hover:bg-gray-100 text-gray-600 md:hidden\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n          <form onSubmit={handleSearch} className=\"hidden md:block\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                placeholder=\"Search...\"\n                className=\"w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n          </form>\n        </div>\n\n        {/* Right side - Notifications and Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <div className=\"relative\" ref={notificationsRef}>\n            <button\n              onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}\n              className=\"p-2 rounded-lg hover:bg-gray-100 text-gray-600 relative\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n              </svg>\n              <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n            </button>\n\n            {/* Notifications Dropdown */}\n            {isNotificationsOpen && (\n              <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\n                <div className=\"px-4 py-2 border-b border-gray-200\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">Notifications</h3>\n                </div>\n                <div className=\"max-h-96 overflow-y-auto\">\n                  {notifications.map((notification) => (\n                    <div\n                      key={notification.id}\n                      className=\"px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-0\"\n                    >\n                      <div className=\"flex items-start\">\n                        <div className=\"flex-shrink-0\">\n                          <div className={`w-2 h-2 rounded-full mt-2 ${\n                            notification.type === 'success' ? 'bg-green-500' :\n                            notification.type === 'warning' ? 'bg-yellow-500' :\n                            'bg-blue-500'\n                          }`}></div>\n                        </div>\n                        <div className=\"ml-3\">\n                          <p className=\"text-sm font-medium text-gray-900\">{notification.title}</p>\n                          <p className=\"text-sm text-gray-500\">{notification.message}</p>\n                          <p className=\"text-xs text-gray-400 mt-1\">{notification.time}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"px-4 py-2 border-t border-gray-200\">\n                  <button className=\"text-sm text-[#2D8C88] hover:text-[#2D8C88]/80 font-medium\">\n                    View all notifications\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Profile */}\n          <div className=\"relative\" ref={profileRef}>\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100\"\n            >\n              <div className=\"w-8 h-8 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n              </div>\n              <div className=\"hidden md:block text-left\">\n                <p className=\"text-sm font-medium text-gray-900\">{user?.name || 'Client User'}</p>\n                <p className=\"text-xs text-gray-500\">{user?.email}</p>\n              </div>\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${\n                  isProfileOpen ? 'rotate-180' : ''\n                }`}\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n\n            {/* Profile Dropdown */}\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\n                <div className=\"px-4 py-2 border-b border-gray-200\">\n                  <p className=\"text-sm font-medium text-gray-900\">Signed in as</p>\n                  <p className=\"text-sm text-gray-500 truncate\">{user?.email}</p>\n                </div>\n                <div className=\"py-1\">\n                  <Link\n                    to=\"/client/settings\"\n                    className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    Settings\n                  </Link>\n                  <button\n                    onClick={() => {\n                      localStorage.clear();\n                      window.location.href = '/login';\n                    }}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100\"\n                  >\n                    Sign out\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Scrollable Menu */}\n      <div className=\"px-4 py-2 border-t border-gray-200 overflow-x-auto\">\n        <div className=\"flex space-x-4\">\n          {menuItems.map((item) => (\n            <Link\n              key={item.path}\n              to={item.path}\n              className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${\n                location.pathname === item.path\n                  ? 'bg-[#2D8C88]/10 text-[#2D8C88]'\n                  : 'text-gray-600 hover:bg-gray-100'\n              }`}\n            >\n              <span className=\"flex-shrink-0\">{item.icon}</span>\n              <span>{item.title}</span>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </motion.nav>\n  );\n};\n\nexport default ClientNavbar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMa,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EAErD,MAAMC,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,OAAO,EAAE,2CAA2C;IACpDC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,uBAAuB;IAC9BC,OAAO,EAAE,0CAA0C;IACnDC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,OAAO,EAAE,2CAA2C;IACpDC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,UAAU,GAAGjB,SAAS,GAAG,gBAAgB,GAAG,iBAAiB;EAEnEkB,SAAS,CAAC,MAAM;IACd,SAASC,kBAAkBA,CAACC,KAAK,EAAE;MACjC,IAAIC,gBAAgB,CAACC,OAAO,IAAI,CAACD,gBAAgB,CAACC,OAAO,CAACC,QAAQ,CAACH,KAAK,CAACI,MAAM,CAAC,EAAE;QAChFrB,sBAAsB,CAAC,KAAK,CAAC;MAC/B;MACA,IAAIsB,UAAU,CAACH,OAAO,IAAI,CAACG,UAAU,CAACH,OAAO,CAACC,QAAQ,CAACH,KAAK,CAACI,MAAM,CAAC,EAAE;QACpEnB,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF;IAEAqB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAER,kBAAkB,CAAC;IAC1D,OAAO,MAAMO,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAET,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,WAAW,CAAC;EAC5C,CAAC;EAED,MAAMC,SAAS,GAAG,CAChB;IACEtB,KAAK,EAAE,WAAW;IAClBuB,IAAI,EAAE,mBAAmB;IACzBC,IAAI,eACFxC,OAAA;MAAKyC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/G9C,OAAA;QAAM+C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3U;EAET,CAAC,EACD;IACEtC,KAAK,EAAE,gBAAgB;IACvBuB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,eACFxC,OAAA;MAAKyC,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/G9C,OAAA;QAAM+C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA2G;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChL;EAET,CAAC,CACF;EAED,oBACEtD,OAAA,CAACF,MAAM,CAACyD,GAAG;IACTC,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBf,SAAS,EAAC,mEAAmE;IAC7EiB,KAAK,EAAE;MAAEC,UAAU,EAAEzD,SAAS,GAAG,EAAE,GAAG;IAAI,CAAE;IAAA2C,QAAA,gBAE5C9C,OAAA;MAAK0C,SAAS,EAAC,6CAA6C;MAAAI,QAAA,gBAE1D9C,OAAA;QAAK0C,SAAS,EAAC,6BAA6B;QAAAI,QAAA,gBAC1C9C,OAAA;UACE6D,OAAO,EAAE3D,aAAc;UACvBwC,SAAS,EAAC,0DAA0D;UAAAI,QAAA,eAEpE9C,OAAA;YAAKyC,KAAK,EAAC,4BAA4B;YAACC,SAAS,EAAC,SAAS;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAC,QAAA,eAC/G9C,OAAA;cAAM+C,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTtD,OAAA;UAAM8D,QAAQ,EAAE9B,YAAa;UAACU,SAAS,EAAC,iBAAiB;UAAAI,QAAA,eACvD9C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvB9C,OAAA;cACEmB,IAAI,EAAC,MAAM;cACX4C,KAAK,EAAE1B,WAAY;cACnB2B,QAAQ,EAAG/B,CAAC,IAAKgC,cAAc,CAAChC,CAAC,CAACN,MAAM,CAACoC,KAAK,CAAE;cAChDG,WAAW,EAAC,WAAW;cACvBxB,SAAS,EAAC;YAAsI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC,eACFtD,OAAA;cACEyC,KAAK,EAAC,4BAA4B;cAClCC,SAAS,EAAC,0EAA0E;cACpFC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAErB9C,OAAA;gBAAM+C,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNtD,OAAA;QAAK0C,SAAS,EAAC,6BAA6B;QAAAI,QAAA,gBAE1C9C,OAAA;UAAK0C,SAAS,EAAC,UAAU;UAACyB,GAAG,EAAE3C,gBAAiB;UAAAsB,QAAA,gBAC9C9C,OAAA;YACE6D,OAAO,EAAEA,CAAA,KAAMvD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;YAC5DqC,SAAS,EAAC,yDAAyD;YAAAI,QAAA,gBAEnE9C,OAAA;cAAKyC,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAC/G9C,OAAA;gBAAM+C,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA+L;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpQ,CAAC,eACNtD,OAAA;cAAM0C,SAAS,EAAC;YAAwD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,EAGRjD,mBAAmB,iBAClBL,OAAA;YAAK0C,SAAS,EAAC,2FAA2F;YAAAI,QAAA,gBACxG9C,OAAA;cAAK0C,SAAS,EAAC,oCAAoC;cAAAI,QAAA,eACjD9C,OAAA;gBAAI0C,SAAS,EAAC,mCAAmC;gBAAAI,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNtD,OAAA;cAAK0C,SAAS,EAAC,0BAA0B;cAAAI,QAAA,EACtChC,aAAa,CAACsD,GAAG,CAAEC,YAAY,iBAC9BrE,OAAA;gBAEE0C,SAAS,EAAC,mEAAmE;gBAAAI,QAAA,eAE7E9C,OAAA;kBAAK0C,SAAS,EAAC,kBAAkB;kBAAAI,QAAA,gBAC/B9C,OAAA;oBAAK0C,SAAS,EAAC,eAAe;oBAAAI,QAAA,eAC5B9C,OAAA;sBAAK0C,SAAS,EAAE,6BACd2B,YAAY,CAAClD,IAAI,KAAK,SAAS,GAAG,cAAc,GAChDkD,YAAY,CAAClD,IAAI,KAAK,SAAS,GAAG,eAAe,GACjD,aAAa;oBACZ;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNtD,OAAA;oBAAK0C,SAAS,EAAC,MAAM;oBAAAI,QAAA,gBACnB9C,OAAA;sBAAG0C,SAAS,EAAC,mCAAmC;sBAAAI,QAAA,EAAEuB,YAAY,CAACrD;oBAAK;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEtD,OAAA;sBAAG0C,SAAS,EAAC,uBAAuB;sBAAAI,QAAA,EAAEuB,YAAY,CAACpD;oBAAO;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/DtD,OAAA;sBAAG0C,SAAS,EAAC,4BAA4B;sBAAAI,QAAA,EAAEuB,YAAY,CAACnD;oBAAI;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAhBDe,YAAY,CAACtD,EAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtD,OAAA;cAAK0C,SAAS,EAAC,oCAAoC;cAAAI,QAAA,eACjD9C,OAAA;gBAAQ0C,SAAS,EAAC,4DAA4D;gBAAAI,QAAA,EAAC;cAE/E;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtD,OAAA;UAAK0C,SAAS,EAAC,UAAU;UAACyB,GAAG,EAAEvC,UAAW;UAAAkB,QAAA,gBACxC9C,OAAA;YACE6D,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChDmC,SAAS,EAAC,8DAA8D;YAAAI,QAAA,gBAExE9C,OAAA;cAAK0C,SAAS,EAAC,sFAAsF;cAAAI,QAAA,eACnG9C,OAAA;gBAAKyC,KAAK,EAAC,4BAA4B;gBAACC,SAAS,EAAC,SAAS;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAC,QAAA,eAC/G9C,OAAA;kBAAM+C,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAqE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtD,OAAA;cAAK0C,SAAS,EAAC,2BAA2B;cAAAI,QAAA,gBACxC9C,OAAA;gBAAG0C,SAAS,EAAC,mCAAmC;gBAAAI,QAAA,EAAE,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI;cAAa;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFtD,OAAA;gBAAG0C,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAErC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNtD,OAAA;cACEyC,KAAK,EAAC,4BAA4B;cAClCC,SAAS,EAAE,2DACTnC,aAAa,GAAG,YAAY,GAAG,EAAE,EAChC;cACHoC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAErB9C,OAAA;gBAAM+C,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAGR/C,aAAa,iBACZP,OAAA;YAAK0C,SAAS,EAAC,2FAA2F;YAAAI,QAAA,gBACxG9C,OAAA;cAAK0C,SAAS,EAAC,oCAAoC;cAAAI,QAAA,gBACjD9C,OAAA;gBAAG0C,SAAS,EAAC,mCAAmC;gBAAAI,QAAA,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEtD,OAAA;gBAAG0C,SAAS,EAAC,gCAAgC;gBAAAI,QAAA,EAAErC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNtD,OAAA;cAAK0C,SAAS,EAAC,MAAM;cAAAI,QAAA,gBACnB9C,OAAA,CAACH,IAAI;gBACH2E,EAAE,EAAC,kBAAkB;gBACrB9B,SAAS,EAAC,yDAAyD;gBAAAI,QAAA,EACpE;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPtD,OAAA;gBACE6D,OAAO,EAAEA,CAAA,KAAM;kBACbjD,YAAY,CAAC6D,KAAK,CAAC,CAAC;kBACpBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;gBACjC,CAAE;gBACFlC,SAAS,EAAC,yEAAyE;gBAAAI,QAAA,EACpF;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAK0C,SAAS,EAAC,oDAAoD;MAAAI,QAAA,eACjE9C,OAAA;QAAK0C,SAAS,EAAC,gBAAgB;QAAAI,QAAA,EAC5BR,SAAS,CAAC8B,GAAG,CAAES,IAAI,iBAClB7E,OAAA,CAACH,IAAI;UAEH2E,EAAE,EAAEK,IAAI,CAACtC,IAAK;UACdG,SAAS,EAAE,0FACTiC,QAAQ,CAACG,QAAQ,KAAKD,IAAI,CAACtC,IAAI,GAC3B,gCAAgC,GAChC,iCAAiC,EACpC;UAAAO,QAAA,gBAEH9C,OAAA;YAAM0C,SAAS,EAAC,eAAe;YAAAI,QAAA,EAAE+B,IAAI,CAACrC;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDtD,OAAA;YAAA8C,QAAA,EAAO+B,IAAI,CAAC7D;UAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATpBuB,IAAI,CAACtC,IAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAClD,EAAA,CApPIH,YAAY;AAAA8E,EAAA,GAAZ9E,YAAY;AAsPlB,eAAeA,YAAY;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}