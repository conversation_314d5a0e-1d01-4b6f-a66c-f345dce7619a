{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\ClientDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { Eye, TrendingUp, Users, ShoppingCart, Clock, Code, Globe, Smartphone } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientDashboard = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [timeRange, setTimeRange] = useState('7d');\n  const [clientData, setClientData] = useState({\n    totalTryOns: 2840,\n    conversionRate: 22.4,\n    avgDuration: 165,\n    // seconds\n    uniqueUsers: 1250,\n    revenue: 45600\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Sample data for charts\n  const tryOnTrends = [{\n    date: '2024-03-01',\n    tryOns: 180,\n    conversions: 42\n  }, {\n    date: '2024-03-02',\n    tryOns: 220,\n    conversions: 48\n  }, {\n    date: '2024-03-03',\n    tryOns: 195,\n    conversions: 44\n  }, {\n    date: '2024-03-04',\n    tryOns: 280,\n    conversions: 65\n  }, {\n    date: '2024-03-05',\n    tryOns: 320,\n    conversions: 72\n  }, {\n    date: '2024-03-06',\n    tryOns: 290,\n    conversions: 68\n  }, {\n    date: '2024-03-07',\n    tryOns: 350,\n    conversions: 78\n  }];\n  const productPerformance = [{\n    name: 'Watch Model A',\n    tryOns: 450,\n    conversions: 98\n  }, {\n    name: 'Watch Model B',\n    tryOns: 380,\n    conversions: 84\n  }, {\n    name: 'Watch Model C',\n    tryOns: 290,\n    conversions: 65\n  }, {\n    name: 'Bracelet A',\n    tryOns: 210,\n    conversions: 47\n  }, {\n    name: 'Bracelet B',\n    tryOns: 180,\n    conversions: 40\n  }];\n  const deviceStats = [{\n    name: 'Mobile',\n    value: 72,\n    color: '#2D8C88'\n  }, {\n    name: 'Desktop',\n    value: 22,\n    color: '#3B82F6'\n  }, {\n    name: 'Tablet',\n    value: 6,\n    color: '#10B981'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Virtual Try-On Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Monitor your product performance and customer engagement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 md:mt-0 flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n              children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setTimeRange(range),\n                className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n                children: range\n              }, range, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n              children: [/*#__PURE__*/_jsxDEV(Code, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), \"Get Embed Code\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clientData.totalTryOns.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+18%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Conversion Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [clientData.conversionRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+2.4%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Avg Duration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [Math.floor(clientData.avgDuration / 60), \"m \", clientData.avgDuration % 60, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+15s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clientData.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+12%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.4\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Revenue Impact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [\"$\", clientData.revenue.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(ShoppingCart, {\n                  className: \"h-6 w-6 text-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+28%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.5\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Try-On Trends\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: tryOnTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"tryOns\",\n                    stroke: \"#2D8C88\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#2D8C88'\n                    },\n                    name: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"conversions\",\n                    stroke: \"#10B981\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#10B981'\n                    },\n                    name: \"Conversions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Device Usage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: deviceStats,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    children: deviceStats.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: entry.color\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.7\n          },\n          className: \"bg-white rounded-xl shadow-sm p-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Top Performing Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-80\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: productPerformance,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"tryOns\",\n                  fill: \"#2D8C88\",\n                  name: \"Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"conversions\",\n                  fill: \"#10B981\",\n                  name: \"Conversions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.8\n          },\n          className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium\",\n                children: \"Ready to integrate Virtual Try-On?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#2D8C88]/80 mt-1\",\n                children: \"Add our try-on button to your product pages in minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                children: \"View Guide\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors\",\n                children: \"Get Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.9\n          },\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Eye, {\n                    className: \"h-6 w-6 text-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"High engagement detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Watch Model A had 50+ try-ons in the last hour\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"1 hour ago\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                    className: \"h-6 w-6 text-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Conversion milestone reached\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Your conversion rate exceeded 22% this week\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"3 hours ago\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Smartphone, {\n                    className: \"h-6 w-6 text-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Mobile usage spike\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"72% of try-ons came from mobile devices today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"5 hours ago\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientDashboard, \"VMH3SB/22WMSBnVtEfOwX6iJ14E=\");\n_c = ClientDashboard;\nexport default ClientDashboard;\nvar _c;\n$RefreshReg$(_c, \"ClientDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ClientSidebar", "ClientNavbar", "motion", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "Eye", "TrendingUp", "Users", "ShoppingCart", "Clock", "Code", "Globe", "Smartphone", "jsxDEV", "_jsxDEV", "ClientDashboard", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "timeRange", "setTimeRange", "clientData", "setClientData", "totalTryOns", "conversionRate", "avgDuration", "uniqueUsers", "revenue", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "tryOnTrends", "date", "tryOns", "conversions", "productPerformance", "name", "deviceStats", "value", "color", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "range", "onClick", "div", "initial", "opacity", "y", "animate", "toLocaleString", "transition", "delay", "Math", "floor", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "cx", "cy", "labelLine", "outerRadius", "label", "percent", "toFixed", "entry", "index", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/ClientDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport ClientSidebar from '../../components/client/ClientSidebar';\r\nimport ClientNavbar from '../../components/client/ClientNavbar';\r\nimport { motion } from 'framer-motion';\r\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\r\nimport { Eye, TrendingUp, Users, ShoppingCart, Clock, Code, Globe, Smartphone } from 'lucide-react';\r\n\r\nconst ClientDashboard = () => {\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [timeRange, setTimeRange] = useState('7d');\r\n  const [clientData, setClientData] = useState({\r\n    totalTryOns: 2840,\r\n    conversionRate: 22.4,\r\n    avgDuration: 165, // seconds\r\n    uniqueUsers: 1250,\r\n    revenue: 45600\r\n  });\r\n\r\n  const toggleSidebar = () => {\r\n    setIsSidebarOpen(!isSidebarOpen);\r\n  };\r\n\r\n  // Calculate margin for main content\r\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\r\n\r\n  // Sample data for charts\r\n  const tryOnTrends = [\r\n    { date: '2024-03-01', tryOns: 180, conversions: 42 },\r\n    { date: '2024-03-02', tryOns: 220, conversions: 48 },\r\n    { date: '2024-03-03', tryOns: 195, conversions: 44 },\r\n    { date: '2024-03-04', tryOns: 280, conversions: 65 },\r\n    { date: '2024-03-05', tryOns: 320, conversions: 72 },\r\n    { date: '2024-03-06', tryOns: 290, conversions: 68 },\r\n    { date: '2024-03-07', tryOns: 350, conversions: 78 },\r\n  ];\r\n\r\n  const productPerformance = [\r\n    { name: 'Watch Model A', tryOns: 450, conversions: 98 },\r\n    { name: 'Watch Model B', tryOns: 380, conversions: 84 },\r\n    { name: 'Watch Model C', tryOns: 290, conversions: 65 },\r\n    { name: 'Bracelet A', tryOns: 210, conversions: 47 },\r\n    { name: 'Bracelet B', tryOns: 180, conversions: 40 },\r\n  ];\r\n\r\n  const deviceStats = [\r\n    { name: 'Mobile', value: 72, color: '#2D8C88' },\r\n    { name: 'Desktop', value: 22, color: '#3B82F6' },\r\n    { name: 'Tablet', value: 6, color: '#10B981' },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\r\n      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\r\n\r\n      {/* Main Content */}\r\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\r\n        <div className=\"p-4 md:p-6\">\r\n          {/* Page Header */}\r\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold text-gray-900\">Virtual Try-On Dashboard</h1>\r\n              <p className=\"text-gray-600\">Monitor your product performance and customer engagement</p>\r\n            </div>\r\n            <div className=\"mt-4 md:mt-0 flex space-x-3\">\r\n              <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\r\n                {['7d', '30d', '90d', '1y'].map((range) => (\r\n                  <button\r\n                    key={range}\r\n                    onClick={() => setTimeRange(range)}\r\n                    className={`px-3 py-1 text-sm font-medium rounded-md ${\r\n                      timeRange === range\r\n                        ? 'bg-[#2D8C88] text-white'\r\n                        : 'text-gray-600 hover:text-gray-900'\r\n                    }`}\r\n                  >\r\n                    {range}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              <button className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\">\r\n                <Code className=\"h-4 w-4 mr-2\" />\r\n                Get Embed Code\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Enhanced Stats Grid */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 mb-6\">\r\n            {/* Total Try-Ons */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.totalTryOns.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\r\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className=\"text-sm font-medium text-green-600\">+18%</span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last week</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Conversion Rate */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.1 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Conversion Rate</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.conversionRate}%</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\r\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className=\"text-sm font-medium text-green-600\">+2.4%</span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Average Duration */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Avg Duration</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{Math.floor(clientData.avgDuration / 60)}m {clientData.avgDuration % 60}s</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\r\n                  <Clock className=\"h-6 w-6 text-blue-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className=\"text-sm font-medium text-green-600\">+15s</span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Unique Users */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.uniqueUsers.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\r\n                  <Users className=\"h-6 w-6 text-purple-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className=\"text-sm font-medium text-green-600\">+12%</span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last week</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Revenue Impact */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.4 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Revenue Impact</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">${clientData.revenue.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center\">\r\n                  <ShoppingCart className=\"h-6 w-6 text-orange-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className=\"text-sm font-medium text-green-600\">+28%</span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Charts Grid */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n            {/* Try-On Trends */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.5 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Try-On Trends</h3>\r\n              <div className=\"h-80\">\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <LineChart data={tryOnTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"date\" />\r\n                    <YAxis />\r\n                    <Tooltip />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"tryOns\"\r\n                      stroke=\"#2D8C88\"\r\n                      strokeWidth={2}\r\n                      dot={{ fill: '#2D8C88' }}\r\n                      name=\"Try-Ons\"\r\n                    />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"conversions\"\r\n                      stroke=\"#10B981\"\r\n                      strokeWidth={2}\r\n                      dot={{ fill: '#10B981' }}\r\n                      name=\"Conversions\"\r\n                    />\r\n                  </LineChart>\r\n                </ResponsiveContainer>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Device Distribution */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.6 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Device Usage</h3>\r\n              <div className=\"h-80\">\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <PieChart>\r\n                    <Pie\r\n                      data={deviceStats}\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      labelLine={false}\r\n                      outerRadius={80}\r\n                      fill=\"#8884d8\"\r\n                      dataKey=\"value\"\r\n                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\r\n                    >\r\n                      {deviceStats.map((entry, index) => (\r\n                        <Cell key={`cell-${index}`} fill={entry.color} />\r\n                      ))}\r\n                    </Pie>\r\n                    <Tooltip />\r\n                  </PieChart>\r\n                </ResponsiveContainer>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Product Performance */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.7 }}\r\n            className=\"bg-white rounded-xl shadow-sm p-6 mb-6\"\r\n          >\r\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Top Performing Products</h3>\r\n            <div className=\"h-80\">\r\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                <BarChart data={productPerformance}>\r\n                  <CartesianGrid strokeDasharray=\"3 3\" />\r\n                  <XAxis dataKey=\"name\" />\r\n                  <YAxis />\r\n                  <Tooltip />\r\n                  <Bar dataKey=\"tryOns\" fill=\"#2D8C88\" name=\"Try-Ons\" />\r\n                  <Bar dataKey=\"conversions\" fill=\"#10B981\" name=\"Conversions\" />\r\n                </BarChart>\r\n              </ResponsiveContainer>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Integration Guide */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.8 }}\r\n            className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6 mb-6\"\r\n          >\r\n            <div className=\"flex items-center justify-between text-white\">\r\n              <div>\r\n                <h3 className=\"text-lg font-medium\">Ready to integrate Virtual Try-On?</h3>\r\n                <p className=\"text-[#2D8C88]/80 mt-1\">Add our try-on button to your product pages in minutes</p>\r\n              </div>\r\n              <div className=\"flex space-x-3\">\r\n                <button className=\"bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors\">\r\n                  View Guide\r\n                </button>\r\n                <button className=\"bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors\">\r\n                  Get Code\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Recent Activity */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.9 }}\r\n            className=\"bg-white rounded-xl shadow-sm overflow-hidden\"\r\n          >\r\n            <div className=\"p-6 border-b border-gray-200\">\r\n              <h2 className=\"text-lg font-medium text-gray-900\">Recent Activity</h2>\r\n            </div>\r\n            <div className=\"divide-y divide-gray-200\">\r\n              {/* Activity Items */}\r\n              <div className=\"p-6\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\r\n                    <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-gray-900\">High engagement detected</p>\r\n                    <p className=\"text-sm text-gray-500\">Watch Model A had 50+ try-ons in the last hour</p>\r\n                  </div>\r\n                  <div className=\"ml-auto\">\r\n                    <p className=\"text-sm text-gray-500\">1 hour ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-6\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center\">\r\n                    <TrendingUp className=\"h-6 w-6 text-green-500\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-gray-900\">Conversion milestone reached</p>\r\n                    <p className=\"text-sm text-gray-500\">Your conversion rate exceeded 22% this week</p>\r\n                  </div>\r\n                  <div className=\"ml-auto\">\r\n                    <p className=\"text-sm text-gray-500\">3 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-6\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"w-10 h-10 rounded-full bg-blue-500/10 flex items-center justify-center\">\r\n                    <Smartphone className=\"h-6 w-6 text-blue-500\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-gray-900\">Mobile usage spike</p>\r\n                    <p className=\"text-sm text-gray-500\">72% of try-ons came from mobile devices today</p>\r\n                  </div>\r\n                  <div className=\"ml-auto\">\r\n                    <p className=\"text-sm text-gray-500\">5 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ClientDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACzI,SAASC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpG,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC;IAC3CqC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,GAAG;IAAE;IAClBC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BZ,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMc,UAAU,GAAGZ,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA,MAAMa,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,CACrD;EAED,MAAMC,kBAAkB,GAAG,CACzB;IAAEC,IAAI,EAAE,eAAe;IAAEH,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACvD;IAAEE,IAAI,EAAE,eAAe;IAAEH,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACvD;IAAEE,IAAI,EAAE,eAAe;IAAEH,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACvD;IAAEE,IAAI,EAAE,YAAY;IAAEH,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EACpD;IAAEE,IAAI,EAAE,YAAY;IAAEH,MAAM,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,CACrD;EAED,MAAMG,WAAW,GAAG,CAClB;IAAED,IAAI,EAAE,QAAQ;IAAEE,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAEH,IAAI,EAAE,SAAS;IAAEE,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAChD;IAAEH,IAAI,EAAE,QAAQ;IAAEE,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC/C;EAED,oBACE1B,OAAA;IAAK2B,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC5B,OAAA,CAACxB,aAAa;MAACqD,MAAM,EAAE1B,aAAc;MAAC2B,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClIlC,OAAA,CAACvB,YAAY;MAACuC,aAAa,EAAEA,aAAc;MAACX,SAAS,EAAEA;IAAU;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpElC,OAAA;MAAM2B,SAAS,EAAE,GAAGV,UAAU,oCAAqC;MAAAW,QAAA,eACjE5B,OAAA;QAAK2B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzB5B,OAAA;UAAK2B,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChF5B,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAI2B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ElC,OAAA;cAAG2B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNlC,OAAA;YAAK2B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5B,OAAA;cAAK2B,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACO,GAAG,CAAEC,KAAK,iBACpCpC,OAAA;gBAEEqC,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAAC4B,KAAK,CAAE;gBACnCT,SAAS,EAAE,4CACTpB,SAAS,KAAK6B,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;gBAAAR,QAAA,EAEFQ;cAAK,GARDA,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlC,OAAA;cAAQ2B,SAAS,EAAC,6KAA6K;cAAAC,QAAA,gBAC7L5B,OAAA,CAACJ,IAAI;gBAAC+B,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK2B,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBAEjF5B,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9Bd,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAK2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5B,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAG2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClElC,OAAA;kBAAG2B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnB,UAAU,CAACE,WAAW,CAACgC,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlC,OAAA;gBAAK2B,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5B,OAAA,CAACT,GAAG;kBAACoC,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5B,OAAA;gBAAM2B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChElC,OAAA;gBAAM2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAK2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5B,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAG2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpElC,OAAA;kBAAG2B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAEnB,UAAU,CAACG,cAAc,EAAC,GAAC;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNlC,OAAA;gBAAK2B,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5B,OAAA,CAACR,UAAU;kBAACmC,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5B,OAAA;gBAAM2B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjElC,OAAA;gBAAM2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAK2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5B,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAG2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjElC,OAAA;kBAAG2B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAEkB,IAAI,CAACC,KAAK,CAACtC,UAAU,CAACI,WAAW,GAAG,EAAE,CAAC,EAAC,IAAE,EAACJ,UAAU,CAACI,WAAW,GAAG,EAAE,EAAC,GAAC;gBAAA;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnI,CAAC,eACNlC,OAAA;gBAAK2B,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrF5B,OAAA,CAACL,KAAK;kBAACgC,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5B,OAAA;gBAAM2B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChElC,OAAA;gBAAM2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAK2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5B,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAG2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjElC,OAAA;kBAAG2B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnB,UAAU,CAACK,WAAW,CAAC6B,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlC,OAAA;gBAAK2B,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF5B,OAAA,CAACP,KAAK;kBAACkC,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5B,OAAA;gBAAM2B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChElC,OAAA;gBAAM2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAK2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5B,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAG2B,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnElC,OAAA;kBAAG2B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAC,GAAC,EAACnB,UAAU,CAACM,OAAO,CAAC4B,cAAc,CAAC,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACNlC,OAAA;gBAAK2B,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF5B,OAAA,CAACN,YAAY;kBAACiC,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5B,OAAA;gBAAM2B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChElC,OAAA;gBAAM2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlC,OAAA;UAAK2B,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEzD5B,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAI2B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzElC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5B,OAAA,CAACb,mBAAmB;gBAAC6D,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAArB,QAAA,eAC7C5B,OAAA,CAACrB,SAAS;kBAACuE,IAAI,EAAEhC,WAAY;kBAAAU,QAAA,gBAC3B5B,OAAA,CAACf,aAAa;oBAACkE,eAAe,EAAC;kBAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvClC,OAAA,CAACjB,KAAK;oBAACqE,OAAO,EAAC;kBAAM;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBlC,OAAA,CAAChB,KAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTlC,OAAA,CAACd,OAAO;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACXlC,OAAA,CAACpB,IAAI;oBACHyE,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,QAAQ;oBAChBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBlC,IAAI,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFlC,OAAA,CAACpB,IAAI;oBACHyE,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,aAAa;oBACrBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBlC,IAAI,EAAC;kBAAa;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5B,OAAA;cAAI2B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxElC,OAAA;cAAK2B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5B,OAAA,CAACb,mBAAmB;gBAAC6D,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAArB,QAAA,eAC7C5B,OAAA,CAACZ,QAAQ;kBAAAwC,QAAA,gBACP5B,OAAA,CAACX,GAAG;oBACF6D,IAAI,EAAE1B,WAAY;oBAClBkC,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjBC,WAAW,EAAE,EAAG;oBAChBJ,IAAI,EAAC,SAAS;oBACdL,OAAO,EAAC,OAAO;oBACfU,KAAK,EAAEA,CAAC;sBAAEvC,IAAI;sBAAEwC;oBAAQ,CAAC,KAAK,GAAGxC,IAAI,IAAI,CAACwC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;oBAAApC,QAAA,EAEtEJ,WAAW,CAACW,GAAG,CAAC,CAAC8B,KAAK,EAAEC,KAAK,kBAC5BlE,OAAA,CAACV,IAAI;sBAAuBmE,IAAI,EAAEQ,KAAK,CAACvC;oBAAM,GAAnC,QAAQwC,KAAK,EAAE;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAsB,CACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNlC,OAAA,CAACd,OAAO;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAElD5B,OAAA;YAAI2B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAuB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFlC,OAAA;YAAK2B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB5B,OAAA,CAACb,mBAAmB;cAAC6D,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAArB,QAAA,eAC7C5B,OAAA,CAACnB,QAAQ;gBAACqE,IAAI,EAAE5B,kBAAmB;gBAAAM,QAAA,gBACjC5B,OAAA,CAACf,aAAa;kBAACkE,eAAe,EAAC;gBAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvClC,OAAA,CAACjB,KAAK;kBAACqE,OAAO,EAAC;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBlC,OAAA,CAAChB,KAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTlC,OAAA,CAACd,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXlC,OAAA,CAAClB,GAAG;kBAACsE,OAAO,EAAC,QAAQ;kBAACK,IAAI,EAAC,SAAS;kBAAClC,IAAI,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtDlC,OAAA,CAAClB,GAAG;kBAACsE,OAAO,EAAC,aAAa;kBAACK,IAAI,EAAC,SAAS;kBAAClC,IAAI,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eAEtF5B,OAAA;YAAK2B,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAC3D5B,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAI2B,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ElC,OAAA;gBAAG2B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5B,OAAA;gBAAQ2B,SAAS,EAAC,6FAA6F;gBAAAC,QAAA,EAAC;cAEhH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlC,OAAA;gBAAQ2B,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAAC;cAElH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGblC,OAAA,CAACtB,MAAM,CAAC4D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAEzD5B,OAAA;YAAK2B,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C5B,OAAA;cAAI2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACNlC,OAAA;YAAK2B,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAEvC5B,OAAA;cAAK2B,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB5B,OAAA;gBAAK2B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5B,OAAA;kBAAK2B,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,eACtF5B,OAAA,CAACT,GAAG;oBAACoC,SAAS,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACNlC,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAG2B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7ElC,OAAA;oBAAG2B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA8C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACNlC,OAAA;kBAAK2B,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtB5B,OAAA;oBAAG2B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB5B,OAAA;gBAAK2B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5B,OAAA;kBAAK2B,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,eACtF5B,OAAA,CAACR,UAAU;oBAACmC,SAAS,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACNlC,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAG2B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAA4B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACjFlC,OAAA;oBAAG2B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA2C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACNlC,OAAA;kBAAK2B,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtB5B,OAAA;oBAAG2B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK2B,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB5B,OAAA;gBAAK2B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5B,OAAA;kBAAK2B,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,eACrF5B,OAAA,CAACF,UAAU;oBAAC6B,SAAS,EAAC;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNlC,OAAA;kBAAA4B,QAAA,gBACE5B,OAAA;oBAAG2B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACvElC,OAAA;oBAAG2B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA6C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNlC,OAAA;kBAAK2B,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtB5B,OAAA;oBAAG2B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChC,EAAA,CAhXID,eAAe;AAAAkE,EAAA,GAAflE,eAAe;AAkXrB,eAAeA,eAAe;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}