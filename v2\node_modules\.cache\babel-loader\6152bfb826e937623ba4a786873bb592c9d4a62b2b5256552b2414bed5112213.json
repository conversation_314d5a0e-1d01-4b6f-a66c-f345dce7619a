{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\components\\\\client\\\\ClientNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientNavbar = ({\n  toggleSidebar,\n  collapsed\n}) => {\n  _s();\n  var _user, _user2, _user3;\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const notifications = [{\n    id: 1,\n    title: 'Try-on Session Completed',\n    message: 'A user completed a virtual try-on session',\n    time: '5 minutes ago',\n    type: 'tryon'\n  }, {\n    id: 2,\n    title: 'High Engagement Alert',\n    message: 'Your product \"Watch Model A\" is trending',\n    time: '1 hour ago',\n    type: 'engagement'\n  }, {\n    id: 3,\n    title: 'Weekly Report Ready',\n    message: 'Your weekly analytics report is available',\n    time: '2 hours ago',\n    type: 'report'\n  }];\n\n  // Set left margin based on collapsed state\n  const leftMargin = collapsed ? 'md:left-[80px]' : 'md:left-[280px]';\n  return /*#__PURE__*/_jsxDEV(motion.nav, {\n    initial: {\n      y: -100\n    },\n    animate: {\n      y: 0\n    },\n    className: `fixed top-0 right-0 left-0 ${leftMargin} h-16 bg-white border-b border-gray-200 z-30 transition-all duration-300`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 md:px-6 h-full flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleSidebar,\n        className: \"md:hidden p-2 rounded-lg hover:bg-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-6 w-6 text-gray-600\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4 6h16M4 12h16M4 18h16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:flex flex-1 max-w-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search...\",\n            className: \"w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"absolute left-3 top-2.5 h-5 w-5 text-gray-400\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsNotificationsOpen(!isNotificationsOpen),\n            className: \"p-2 text-gray-400 hover:text-[#2D8C88] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), isNotificationsOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-2 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-96 overflow-y-auto\",\n              children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-2 h-2 rounded-full mt-2 ${notification.type === 'success' ? 'bg-green-500' : notification.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: notification.time\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 23\n                }, this)\n              }, notification.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-2 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"text-sm text-[#2D8C88] hover:text-[#2D8C88]/80 font-medium\",\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: profileRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsProfileOpen(!isProfileOpen),\n            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: ((_user = user) === null || _user === void 0 ? void 0 : _user.name) || 'Client User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: (_user2 = user) === null || _user2 === void 0 ? void 0 : _user2.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: `h-5 w-5 text-gray-400 transition-transform duration-200 ${isProfileOpen ? 'rotate-180' : ''}`,\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M19 9l-7 7-7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), isProfileOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-2 border-b border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Signed in as\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 truncate\",\n                children: (_user3 = user) === null || _user3 === void 0 ? void 0 : _user3.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/client/settings\",\n                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  localStorage.clear();\n                  window.location.href = '/login';\n                },\n                className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100\",\n                children: \"Sign out\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-2 border-t border-gray-200 overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${location.pathname === item.path ? 'bg-[#2D8C88]/10 text-[#2D8C88]' : 'text-gray-600 hover:bg-gray-100'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex-shrink-0\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientNavbar, \"y1njsZR1LlYOnzS9kewAN57ChI4=\");\n_c = ClientNavbar;\nexport default ClientNavbar;\nvar _c;\n$RefreshReg$(_c, \"ClientNavbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "motion", "jsxDEV", "_jsxDEV", "ClientNavbar", "toggleSidebar", "collapsed", "_s", "_user", "_user2", "_user3", "isNotificationsOpen", "setIsNotificationsOpen", "isProfileOpen", "setIsProfileOpen", "notifications", "id", "title", "message", "time", "type", "leftMargin", "nav", "initial", "y", "animate", "className", "children", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "map", "notification", "ref", "profileRef", "user", "name", "email", "to", "localStorage", "clear", "window", "location", "href", "menuItems", "item", "path", "pathname", "icon", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/components/client/ClientNavbar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\nconst ClientNavbar = ({ toggleSidebar, collapsed }) => {\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n\n  const notifications = [\n    {\n      id: 1,\n      title: 'Try-on Session Completed',\n      message: 'A user completed a virtual try-on session',\n      time: '5 minutes ago',\n      type: 'tryon'\n    },\n    {\n      id: 2,\n      title: 'High Engagement Alert',\n      message: 'Your product \"Watch Model A\" is trending',\n      time: '1 hour ago',\n      type: 'engagement'\n    },\n    {\n      id: 3,\n      title: 'Weekly Report Ready',\n      message: 'Your weekly analytics report is available',\n      time: '2 hours ago',\n      type: 'report'\n    }\n  ];\n\n  // Set left margin based on collapsed state\n  const leftMargin = collapsed ? 'md:left-[80px]' : 'md:left-[280px]';\n\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 right-0 left-0 ${leftMargin} h-16 bg-white border-b border-gray-200 z-30 transition-all duration-300`}\n    >\n      <div className=\"px-4 md:px-6 h-full flex items-center justify-between\">\n        {/* Mobile Menu Button */}\n        <button\n          onClick={toggleSidebar}\n          className=\"md:hidden p-2 rounded-lg hover:bg-gray-100\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className=\"h-6 w-6 text-gray-600\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M4 6h16M4 12h16M4 18h16\"\n            />\n          </svg>\n        </button>\n\n        {/* Left side - Search */}\n        <div className=\"hidden md:flex flex-1 max-w-md\">\n          <div className=\"relative w-full\">\n            <input\n              type=\"text\"\n              placeholder=\"Search...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            />\n            <svg\n              className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              />\n            </svg>\n          </div>\n        </div>\n\n        {/* Right side - Notifications and Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}\n              className=\"p-2 text-gray-400 hover:text-[#2D8C88] relative\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-6 w-6\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                />\n              </svg>\n              <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n            </button>\n\n            {/* Notifications Dropdown */}\n            {isNotificationsOpen && (\n              <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\n                <div className=\"px-4 py-2 border-b border-gray-200\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">Notifications</h3>\n                </div>\n                <div className=\"max-h-96 overflow-y-auto\">\n                  {notifications.map((notification) => (\n                    <div\n                      key={notification.id}\n                      className=\"px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-0\"\n                    >\n                      <div className=\"flex items-start\">\n                        <div className=\"flex-shrink-0\">\n                          <div className={`w-2 h-2 rounded-full mt-2 ${\n                            notification.type === 'success' ? 'bg-green-500' :\n                            notification.type === 'warning' ? 'bg-yellow-500' :\n                            'bg-blue-500'\n                          }`}></div>\n                        </div>\n                        <div className=\"ml-3\">\n                          <p className=\"text-sm font-medium text-gray-900\">{notification.title}</p>\n                          <p className=\"text-sm text-gray-500\">{notification.message}</p>\n                          <p className=\"text-xs text-gray-400 mt-1\">{notification.time}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"px-4 py-2 border-t border-gray-200\">\n                  <button className=\"text-sm text-[#2D8C88] hover:text-[#2D8C88]/80 font-medium\">\n                    View all notifications\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Profile */}\n          <div className=\"relative\" ref={profileRef}>\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100\"\n            >\n              <div className=\"w-8 h-8 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n              </div>\n              <div className=\"hidden md:block text-left\">\n                <p className=\"text-sm font-medium text-gray-900\">{user?.name || 'Client User'}</p>\n                <p className=\"text-xs text-gray-500\">{user?.email}</p>\n              </div>\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${\n                  isProfileOpen ? 'rotate-180' : ''\n                }`}\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n\n            {/* Profile Dropdown */}\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\n                <div className=\"px-4 py-2 border-b border-gray-200\">\n                  <p className=\"text-sm font-medium text-gray-900\">Signed in as</p>\n                  <p className=\"text-sm text-gray-500 truncate\">{user?.email}</p>\n                </div>\n                <div className=\"py-1\">\n                  <Link\n                    to=\"/client/settings\"\n                    className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    Settings\n                  </Link>\n                  <button\n                    onClick={() => {\n                      localStorage.clear();\n                      window.location.href = '/login';\n                    }}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100\"\n                  >\n                    Sign out\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Scrollable Menu */}\n      <div className=\"px-4 py-2 border-t border-gray-200 overflow-x-auto\">\n        <div className=\"flex space-x-4\">\n          {menuItems.map((item) => (\n            <Link\n              key={item.path}\n              to={item.path}\n              className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${\n                location.pathname === item.path\n                  ? 'bg-[#2D8C88]/10 text-[#2D8C88]'\n                  : 'text-gray-600 hover:bg-gray-100'\n              }`}\n            >\n              <span className=\"flex-shrink-0\">{item.icon}</span>\n              <span>{item.title}</span>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </motion.nav>\n  );\n};\n\nexport default ClientNavbar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,KAAA,EAAAC,MAAA,EAAAC,MAAA;EACrD,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMgB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,OAAO,EAAE,2CAA2C;IACpDC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,uBAAuB;IAC9BC,OAAO,EAAE,0CAA0C;IACnDC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,OAAO,EAAE,2CAA2C;IACpDC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,UAAU,GAAGf,SAAS,GAAG,gBAAgB,GAAG,iBAAiB;EAGnE,oBACEH,OAAA,CAACF,MAAM,CAACqB,GAAG;IACTC,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,SAAS,EAAE,8BAA8BL,UAAU,0EAA2E;IAAAM,QAAA,gBAE9HxB,OAAA;MAAKuB,SAAS,EAAC,uDAAuD;MAAAC,QAAA,gBAEpExB,OAAA;QACEyB,OAAO,EAAEvB,aAAc;QACvBqB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eAEtDxB,OAAA;UACE0B,KAAK,EAAC,4BAA4B;UAClCH,SAAS,EAAC,uBAAuB;UACjCI,IAAI,EAAC,MAAM;UACXC,OAAO,EAAC,WAAW;UACnBC,MAAM,EAAC,cAAc;UAAAL,QAAA,eAErBxB,OAAA;YACE8B,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBC,WAAW,EAAE,CAAE;YACfC,CAAC,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGTrC,OAAA;QAAKuB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CxB,OAAA;UAAKuB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxB,OAAA;YACEiB,IAAI,EAAC,MAAM;YACXqB,WAAW,EAAC,WAAW;YACvBf,SAAS,EAAC;UAAwI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnJ,CAAC,eACFrC,OAAA;YACEuB,SAAS,EAAC,+CAA+C;YACzDG,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAL,QAAA,eAErBxB,OAAA;cACE8B,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKuB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CxB,OAAA;UAAKuB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBxB,OAAA;YACEyB,OAAO,EAAEA,CAAA,KAAMhB,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;YAC5De,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAE3DxB,OAAA;cACE0B,KAAK,EAAC,4BAA4B;cAClCH,SAAS,EAAC,SAAS;cACnBI,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAL,QAAA,eAErBxB,OAAA;gBACE8B,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAA+L;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrC,OAAA;cAAMuB,SAAS,EAAC;YAAwD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,EAGR7B,mBAAmB,iBAClBR,OAAA;YAAKuB,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBACxGxB,OAAA;cAAKuB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDxB,OAAA;gBAAIuB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAa;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNrC,OAAA;cAAKuB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCZ,aAAa,CAAC2B,GAAG,CAAEC,YAAY,iBAC9BxC,OAAA;gBAEEuB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAE7ExB,OAAA;kBAAKuB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BxB,OAAA;oBAAKuB,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BxB,OAAA;sBAAKuB,SAAS,EAAE,6BACdiB,YAAY,CAACvB,IAAI,KAAK,SAAS,GAAG,cAAc,GAChDuB,YAAY,CAACvB,IAAI,KAAK,SAAS,GAAG,eAAe,GACjD,aAAa;oBACZ;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNrC,OAAA;oBAAKuB,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBxB,OAAA;sBAAGuB,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEgB,YAAY,CAAC1B;oBAAK;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzErC,OAAA;sBAAGuB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEgB,YAAY,CAACzB;oBAAO;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/DrC,OAAA;sBAAGuB,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEgB,YAAY,CAACxB;oBAAI;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAhBDG,YAAY,CAAC3B,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrC,OAAA;cAAKuB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDxB,OAAA;gBAAQuB,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EAAC;cAE/E;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrC,OAAA;UAAKuB,SAAS,EAAC,UAAU;UAACkB,GAAG,EAAEC,UAAW;UAAAlB,QAAA,gBACxCxB,OAAA;YACEyB,OAAO,EAAEA,CAAA,KAAMd,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChDa,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAExExB,OAAA;cAAKuB,SAAS,EAAC,sFAAsF;cAAAC,QAAA,eACnGxB,OAAA;gBAAK0B,KAAK,EAAC,4BAA4B;gBAACH,SAAS,EAAC,SAAS;gBAACI,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAL,QAAA,eAC/GxB,OAAA;kBAAM8B,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAqE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrC,OAAA;cAAKuB,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCxB,OAAA;gBAAGuB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAE,EAAAnB,KAAA,GAAAsC,IAAI,cAAAtC,KAAA,uBAAJA,KAAA,CAAMuC,IAAI,KAAI;cAAa;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFrC,OAAA;gBAAGuB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAAlB,MAAA,GAAEqC,IAAI,cAAArC,MAAA,uBAAJA,MAAA,CAAMuC;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNrC,OAAA;cACE0B,KAAK,EAAC,4BAA4B;cAClCH,SAAS,EAAE,2DACTb,aAAa,GAAG,YAAY,GAAG,EAAE,EAChC;cACHiB,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAL,QAAA,eAErBxB,OAAA;gBAAM8B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAGR3B,aAAa,iBACZV,OAAA;YAAKuB,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBACxGxB,OAAA;cAAKuB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjDxB,OAAA;gBAAGuB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjErC,OAAA;gBAAGuB,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,GAAAjB,MAAA,GAAEoC,IAAI,cAAApC,MAAA,uBAAJA,MAAA,CAAMsC;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNrC,OAAA;cAAKuB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxB,OAAA,CAACH,IAAI;gBACHiD,EAAE,EAAC,kBAAkB;gBACrBvB,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EACpE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrC,OAAA;gBACEyB,OAAO,EAAEA,CAAA,KAAM;kBACbsB,YAAY,CAACC,KAAK,CAAC,CAAC;kBACpBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;gBACjC,CAAE;gBACF5B,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,EACpF;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKuB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACjExB,OAAA;QAAKuB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5B4B,SAAS,CAACb,GAAG,CAAEc,IAAI,iBAClBrD,OAAA,CAACH,IAAI;UAEHiD,EAAE,EAAEO,IAAI,CAACC,IAAK;UACd/B,SAAS,EAAE,0FACT2B,QAAQ,CAACK,QAAQ,KAAKF,IAAI,CAACC,IAAI,GAC3B,gCAAgC,GAChC,iCAAiC,EACpC;UAAA9B,QAAA,gBAEHxB,OAAA;YAAMuB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE6B,IAAI,CAACG;UAAI;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDrC,OAAA;YAAAwB,QAAA,EAAO6B,IAAI,CAACvC;UAAK;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATpBgB,IAAI,CAACC,IAAI;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACjC,EAAA,CAnOIH,YAAY;AAAAwD,EAAA,GAAZxD,YAAY;AAqOlB,eAAeA,YAAY;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}