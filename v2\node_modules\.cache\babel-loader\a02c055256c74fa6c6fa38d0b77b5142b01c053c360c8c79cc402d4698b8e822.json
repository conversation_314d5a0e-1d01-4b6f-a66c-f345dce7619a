{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [clientForm, setClientForm] = useState({\n    name: '',\n    website: '',\n    email: '',\n    password: ''\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Sample client data\n  const clients = [{\n    id: 1,\n    name: 'John Doe',\n    email: '<EMAIL>',\n    status: 'active',\n    joinDate: '2024-01-15'\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    email: '<EMAIL>',\n    status: 'pending',\n    joinDate: '2024-02-01'\n  }\n  // Add more sample clients as needed\n  ];\n  const filteredClients = clients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) || client.email.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesStatus = selectedStatus === 'all' || client.status === selectedStatus;\n    return matchesSearch && matchesStatus;\n  });\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const handleAddClient = e => {\n    e.preventDefault();\n    // Here you would handle adding the client (API call, etc.)\n    setShowModal(false);\n    setClientForm({\n      name: '',\n      website: '',\n      email: '',\n      password: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Clients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your client accounts and information.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: () => setShowModal(true),\n            children: \"+ Add Client\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600\",\n              onClick: () => setShowModal(false),\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Add Client\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleAddClient,\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Client Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: clientForm.name,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"website\",\n                  value: clientForm.website,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: clientForm.email,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"password\",\n                    value: clientForm.password,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: handleSuggestPassword,\n                    className: \"mt-1 px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300\",\n                    children: \"Suggest Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: \"Add Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Join Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: filteredClients.map(client => /*#__PURE__*/_jsxDEV(motion.tr, {\n                  initial: {\n                    opacity: 0\n                  },\n                  animate: {\n                    opacity: 1\n                  },\n                  className: \"hover:bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 h-10 w-10\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                          children: client.name.charAt(0)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 218,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm font-medium text-gray-900\",\n                          children: client.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 223,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-500 md:hidden\",\n                          children: client.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 224,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-500 md:hidden\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                            children: client.status\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 226,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 225,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-900\",\n                      children: client.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                      children: client.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-900\",\n                      children: client.joinDate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 mr-3\",\n                      children: \"Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"text-red-600 hover:text-red-800\",\n                      children: \"Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this)]\n                }, client.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"JcU09qT3NS/Rx7Rdyt2haUtojkc=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "jsxDEV", "_jsxDEV", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "clientForm", "setClientForm", "name", "website", "email", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "clients", "id", "status", "joinDate", "filteredClients", "filter", "client", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "handleFormChange", "e", "value", "target", "prev", "handleSuggestPassword", "handleAddClient", "preventDefault", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "onChange", "required", "placeholder", "map", "tr", "initial", "opacity", "animate", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [clientForm, setClientForm] = useState({\n    name: '',\n    website: '',\n    email: '',\n    password: '',\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Sample client data\n  const clients = [\n    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', joinDate: '2024-01-15' },\n    { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'pending', joinDate: '2024-02-01' },\n    // Add more sample clients as needed\n  ];\n\n  const filteredClients = clients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         client.email.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesStatus = selectedStatus === 'all' || client.status === selectedStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const handleAddClient = (e) => {\n    e.preventDefault();\n    // Here you would handle adding the client (API call, etc.)\n    setShowModal(false);\n    setClientForm({ name: '', website: '', email: '', password: '' });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Clients</h1>\n              <p className=\"text-gray-600\">Manage your client accounts and information.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={() => setShowModal(true)}\n            >\n              + Add Client\n            </button>\n          </div>\n\n          {/* Add Client Modal */}\n          {showModal && (\n            <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n              <div className=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative\">\n                <button\n                  className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600\"\n                  onClick={() => setShowModal(false)}\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n                <h2 className=\"text-xl font-semibold mb-4\">Add Client</h2>\n                <form onSubmit={handleAddClient} className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Client Name</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={clientForm.name}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Website</label>\n                    <input\n                      type=\"text\"\n                      name=\"website\"\n                      value={clientForm.website}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={clientForm.email}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n                    <div className=\"flex gap-2\">\n                      <input\n                        type=\"text\"\n                        name=\"password\"\n                        value={clientForm.password}\n                        onChange={handleFormChange}\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={handleSuggestPassword}\n                        className=\"mt-1 px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300\"\n                      >\n                        Suggest Password\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"flex justify-end\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                    >\n                      Add Client\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </div>\n          )}\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Email</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Join Date</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {filteredClients.map((client) => (\n                    <motion.tr\n                      key={client.id}\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"hover:bg-gray-50\"\n                    >\n                      <td className=\"px-4 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                              {client.name.charAt(0)}\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{client.name}</div>\n                            <div className=\"text-sm text-gray-500 md:hidden\">{client.email}</div>\n                            <div className=\"text-sm text-gray-500 md:hidden\">\n                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                                client.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\n                              }`}>\n                                {client.status}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                        <div className=\"text-sm text-gray-900\">{client.email}</div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          client.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\n                        }`}>\n                          {client.status}\n                        </span>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                        <div className=\"text-sm text-gray-900\">{client.joinDate}</div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <button className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 mr-3\">Edit</button>\n                        <button className=\"text-red-600 hover:text-red-800\">Delete</button>\n                      </td>\n                    </motion.tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC;IAC3CsC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTvB,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1Bd,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMgB,UAAU,GAAGd,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA,MAAMe,OAAO,GAAG,CACd;IAAEC,EAAE,EAAE,CAAC;IAAEN,IAAI,EAAE,UAAU;IAAEE,KAAK,EAAE,kBAAkB;IAAEK,MAAM,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAa,CAAC,EAChG;IAAEF,EAAE,EAAE,CAAC;IAAEN,IAAI,EAAE,YAAY;IAAEE,KAAK,EAAE,kBAAkB;IAAEK,MAAM,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAa;EAClG;EAAA,CACD;EAED,MAAMC,eAAe,GAAGJ,OAAO,CAACK,MAAM,CAACC,MAAM,IAAI;IAC/C,MAAMC,aAAa,GAAGD,MAAM,CAACX,IAAI,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,WAAW,CAACqB,WAAW,CAAC,CAAC,CAAC,IAC9DF,MAAM,CAACT,KAAK,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,WAAW,CAACqB,WAAW,CAAC,CAAC,CAAC;IACnF,MAAME,aAAa,GAAGrB,cAAc,KAAK,KAAK,IAAIiB,MAAM,CAACJ,MAAM,KAAKb,cAAc;IAClF,OAAOkB,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEjB,IAAI;MAAEkB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCpB,aAAa,CAACqB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACpB,IAAI,GAAGkB;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClCtB,aAAa,CAACqB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzC,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAM6C,eAAe,GAAIL,CAAC,IAAK;IAC7BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB;IACA1B,YAAY,CAAC,KAAK,CAAC;IACnBE,aAAa,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEvB,QAAQ,EAAE;IAAG,CAAC,CAAC;EACnE,CAAC;EAED,oBACEH,OAAA;IAAKgD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCjD,OAAA,CAACb,YAAY;MAAC+D,MAAM,EAAEtC,aAAc;MAACuC,OAAO,EAAEA,CAAA,KAAMtC,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjIvD,OAAA,CAACZ,WAAW;MAACuC,aAAa,EAAEA,aAAc;MAACb,SAAS,EAAEA;IAAU;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnEvD,OAAA;MAAMgD,SAAS,EAAE,GAAGpB,UAAU,oCAAqC;MAAAqB,QAAA,eACjEjD,OAAA;QAAKgD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBjD,OAAA;UAAKgD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFjD,OAAA;YAAAiD,QAAA,gBACEjD,OAAA;cAAIgD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DvD,OAAA;cAAGgD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA4C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNvD,OAAA;YACEgD,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,IAAI,CAAE;YAAA4B,QAAA,EACnC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLnC,SAAS,iBACRpB,OAAA;UAAKgD,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzFjD,OAAA;YAAKgD,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzEjD,OAAA;cACEgD,SAAS,EAAC,0DAA0D;cACpEQ,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,KAAK,CAAE;cAAA4B,QAAA,eAEnCjD,OAAA;gBAAKyD,KAAK,EAAC,4BAA4B;gBAACT,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAX,QAAA,eAC/GjD,OAAA;kBAAM6D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTvD,OAAA;cAAIgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DvD,OAAA;cAAMiE,QAAQ,EAAEnB,eAAgB;cAACE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACpDjD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOgD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9EvD,OAAA;kBACEkE,IAAI,EAAC,MAAM;kBACX1C,IAAI,EAAC,MAAM;kBACXkB,KAAK,EAAEpB,UAAU,CAACE,IAAK;kBACvB2C,QAAQ,EAAE3B,gBAAiB;kBAC3B4B,QAAQ;kBACRpB,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOgD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1EvD,OAAA;kBACEkE,IAAI,EAAC,MAAM;kBACX1C,IAAI,EAAC,SAAS;kBACdkB,KAAK,EAAEpB,UAAU,CAACG,OAAQ;kBAC1B0C,QAAQ,EAAE3B,gBAAiB;kBAC3B4B,QAAQ;kBACRpB,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOgD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEvD,OAAA;kBACEkE,IAAI,EAAC,OAAO;kBACZ1C,IAAI,EAAC,OAAO;kBACZkB,KAAK,EAAEpB,UAAU,CAACI,KAAM;kBACxByC,QAAQ,EAAE3B,gBAAiB;kBAC3B4B,QAAQ;kBACRpB,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAOgD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3EvD,OAAA;kBAAKgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjD,OAAA;oBACEkE,IAAI,EAAC,MAAM;oBACX1C,IAAI,EAAC,UAAU;oBACfkB,KAAK,EAAEpB,UAAU,CAACnB,QAAS;oBAC3BgE,QAAQ,EAAE3B,gBAAiB;oBAC3B4B,QAAQ;oBACRpB,SAAS,EAAC;kBAA+G;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC,eACFvD,OAAA;oBACEkE,IAAI,EAAC,QAAQ;oBACbV,OAAO,EAAEX,qBAAsB;oBAC/BG,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EACzE;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAKgD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BjD,OAAA;kBACEkE,IAAI,EAAC,QAAQ;kBACblB,SAAS,EAAC,6NAA6N;kBAAAC,QAAA,EACxO;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDvD,OAAA;UAAKgD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDjD,OAAA;YAAKgD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CjD,OAAA;cAAKgD,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBjD,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/B3B,KAAK,EAAE1B,WAAY;gBACnBmD,QAAQ,EAAG1B,CAAC,IAAKxB,cAAc,CAACwB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;gBAChDM,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvD,OAAA;cAAKgD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BjD,OAAA;gBACE0C,KAAK,EAAExB,cAAe;gBACtBiD,QAAQ,EAAG1B,CAAC,IAAKtB,iBAAiB,CAACsB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;gBACnDM,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5IjD,OAAA;kBAAQ0C,KAAK,EAAC,KAAK;kBAAAO,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCvD,OAAA;kBAAQ0C,KAAK,EAAC,QAAQ;kBAAAO,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCvD,OAAA;kBAAQ0C,KAAK,EAAC,SAAS;kBAAAO,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvD,OAAA;UAAKgD,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DjD,OAAA;YAAKgD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BjD,OAAA;cAAOgD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpDjD,OAAA;gBAAOgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3BjD,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAIgD,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GvD,OAAA;oBAAIgD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9HvD,OAAA;oBAAIgD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/HvD,OAAA;oBAAIgD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClIvD,OAAA;oBAAIgD,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRvD,OAAA;gBAAOgD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDhB,eAAe,CAACqC,GAAG,CAAEnC,MAAM,iBAC1BnC,OAAA,CAACX,MAAM,CAACkF,EAAE;kBAERC,OAAO,EAAE;oBAAEC,OAAO,EAAE;kBAAE,CAAE;kBACxBC,OAAO,EAAE;oBAAED,OAAO,EAAE;kBAAE,CAAE;kBACxBzB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAE5BjD,OAAA;oBAAIgD,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzCjD,OAAA;sBAAKgD,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCjD,OAAA;wBAAKgD,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,eACtCjD,OAAA;0BAAKgD,SAAS,EAAC,iFAAiF;0BAAAC,QAAA,EAC7Fd,MAAM,CAACX,IAAI,CAACnB,MAAM,CAAC,CAAC;wBAAC;0BAAA+C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNvD,OAAA;wBAAKgD,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBjD,OAAA;0BAAKgD,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAEd,MAAM,CAACX;wBAAI;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtEvD,OAAA;0BAAKgD,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAEd,MAAM,CAACT;wBAAK;0BAAA0B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACrEvD,OAAA;0BAAKgD,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,eAC9CjD,OAAA;4BAAMgD,SAAS,EAAE,2EACfb,MAAM,CAACJ,MAAM,KAAK,QAAQ,GAAG,6BAA6B,GAAG,+BAA+B,EAC3F;4BAAAkB,QAAA,EACAd,MAAM,CAACJ;0BAAM;4BAAAqB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLvD,OAAA;oBAAIgD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC9DjD,OAAA;sBAAKgD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEd,MAAM,CAACT;oBAAK;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLvD,OAAA;oBAAIgD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC9DjD,OAAA;sBAAMgD,SAAS,EAAE,2EACfb,MAAM,CAACJ,MAAM,KAAK,QAAQ,GAAG,6BAA6B,GAAG,+BAA+B,EAC3F;sBAAAkB,QAAA,EACAd,MAAM,CAACJ;oBAAM;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLvD,OAAA;oBAAIgD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC9DjD,OAAA;sBAAKgD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEd,MAAM,CAACH;oBAAQ;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACLvD,OAAA;oBAAIgD,SAAS,EAAC,4DAA4D;oBAAAC,QAAA,gBACxEjD,OAAA;sBAAQgD,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC7EvD,OAAA;sBAAQgD,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA,GAzCApB,MAAM,CAACL,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0CL,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAtPID,OAAO;AAAAiE,EAAA,GAAPjE,OAAO;AAwPb,eAAeA,OAAO;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}