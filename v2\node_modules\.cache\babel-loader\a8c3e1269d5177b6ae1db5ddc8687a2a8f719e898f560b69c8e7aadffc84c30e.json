{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [clientForm, setClientForm] = useState({\n    name: '',\n    website: '',\n    email: '',\n    password: ''\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Enhanced clients data with virtual try-on metrics\n  const clients = [{\n    id: 1,\n    name: 'Luxury Watches Co.',\n    website: 'luxurywatches.com',\n    email: '<EMAIL>',\n    status: 'active',\n    joinDate: '2024-01-15',\n    tryOns: 3420,\n    conversion: '22.4%',\n    revenue: 45600,\n    products: 24,\n    lastActive: '2 hours ago',\n    plan: 'Premium',\n    integration: 'Complete'\n  }, {\n    id: 2,\n    name: 'Elegant Jewelry',\n    website: 'elegantjewelry.com',\n    email: '<EMAIL>',\n    status: 'active',\n    joinDate: '2024-02-03',\n    tryOns: 2890,\n    conversion: '19.8%',\n    revenue: 38200,\n    products: 18,\n    lastActive: '1 day ago',\n    plan: 'Professional',\n    integration: 'Complete'\n  }, {\n    id: 3,\n    name: 'Fashion Accessories',\n    website: 'fashionaccessories.com',\n    email: '<EMAIL>',\n    status: 'active',\n    joinDate: '2024-01-28',\n    tryOns: 2340,\n    conversion: '16.5%',\n    revenue: 28900,\n    products: 32,\n    lastActive: '3 hours ago',\n    plan: 'Professional',\n    integration: 'Partial'\n  }, {\n    id: 4,\n    name: 'Premium Brands',\n    website: 'premiumbrands.com',\n    email: '<EMAIL>',\n    status: 'active',\n    joinDate: '2024-03-10',\n    tryOns: 1980,\n    conversion: '21.2%',\n    revenue: 32400,\n    products: 15,\n    lastActive: '5 hours ago',\n    plan: 'Premium',\n    integration: 'Complete'\n  }, {\n    id: 5,\n    name: 'Style Studio',\n    website: 'stylestudio.com',\n    email: '<EMAIL>',\n    status: 'pending',\n    joinDate: '2024-02-20',\n    tryOns: 1650,\n    conversion: '18.9%',\n    revenue: 24800,\n    products: 12,\n    lastActive: '2 weeks ago',\n    plan: 'Basic',\n    integration: 'Pending'\n  }];\n  const filteredClients = clients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) || client.email.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesStatus = selectedStatus === 'all' || client.status === selectedStatus;\n    return matchesSearch && matchesStatus;\n  });\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const handleAddClient = e => {\n    e.preventDefault();\n    // Here you would handle adding the client (API call, etc.)\n    setShowModal(false);\n    setClientForm({\n      name: '',\n      website: '',\n      email: '',\n      password: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: () => setShowModal(true),\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+2 new\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clients.filter(c => c.status === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"80%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clients.reduce((sum, c) => sum + c.tryOns, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+15%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [\"$\", clients.reduce((sum, c) => sum + c.revenue, 0).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Globe, {\n                  className: \"h-6 w-6 text-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: \"+22%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600\",\n              onClick: () => setShowModal(false),\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Add Client\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleAddClient,\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Client Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: clientForm.name,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"website\",\n                  value: clientForm.website,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: clientForm.email,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"password\",\n                    value: clientForm.password,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: handleSuggestPassword,\n                    className: \"mt-1 px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300\",\n                    children: \"Suggest Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: \"Add Client\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Conversion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: filteredClients.map(client => /*#__PURE__*/_jsxDEV(motion.tr, {\n                  initial: {\n                    opacity: 0\n                  },\n                  animate: {\n                    opacity: 1\n                  },\n                  className: \"hover:bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 h-10 w-10\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                          children: client.name.charAt(0)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 378,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm font-medium text-gray-900\",\n                          children: client.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 383,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-500\",\n                          children: client.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 384,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-500 lg:hidden\",\n                          children: [client.tryOns.toLocaleString(), \" try-ons \\u2022 \", client.conversion, \" conversion\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 385,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: client.tryOns.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [client.products, \" products\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: client.conversion\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"$\", client.revenue.toLocaleString(), \" revenue\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col space-y-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                        children: client.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: client.lastActive\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 406,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.integration === 'Complete' ? 'bg-green-100 text-green-800' : client.integration === 'Partial' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                      children: client.integration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-end space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-1\",\n                        children: /*#__PURE__*/_jsxDEV(Eye, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 420,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"text-blue-600 hover:text-blue-800 p-1\",\n                        children: /*#__PURE__*/_jsxDEV(Code, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"text-gray-600 hover:text-gray-800 p-1\",\n                        children: /*#__PURE__*/_jsxDEV(Edit, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 426,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 425,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"text-red-600 hover:text-red-800 p-1\",\n                        children: /*#__PURE__*/_jsxDEV(Trash2, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this)]\n                }, client.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"JcU09qT3NS/Rx7Rdyt2haUtojkc=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "jsxDEV", "_jsxDEV", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "clientForm", "setClientForm", "name", "website", "email", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "clients", "id", "status", "joinDate", "tryOns", "conversion", "revenue", "products", "lastActive", "plan", "integration", "filteredClients", "filter", "client", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "handleFormChange", "e", "value", "target", "prev", "handleSuggestPassword", "handleAddClient", "preventDefault", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "c", "reduce", "sum", "toLocaleString", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "onChange", "required", "placeholder", "map", "tr", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [clientForm, setClientForm] = useState({\n    name: '',\n    website: '',\n    email: '',\n    password: '',\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Enhanced clients data with virtual try-on metrics\n  const clients = [\n    {\n      id: 1,\n      name: 'Luxury Watches Co.',\n      website: 'luxurywatches.com',\n      email: '<EMAIL>',\n      status: 'active',\n      joinDate: '2024-01-15',\n      tryOns: 3420,\n      conversion: '22.4%',\n      revenue: 45600,\n      products: 24,\n      lastActive: '2 hours ago',\n      plan: 'Premium',\n      integration: 'Complete'\n    },\n    {\n      id: 2,\n      name: 'Elegant Jewelry',\n      website: 'elegantjewelry.com',\n      email: '<EMAIL>',\n      status: 'active',\n      joinDate: '2024-02-03',\n      tryOns: 2890,\n      conversion: '19.8%',\n      revenue: 38200,\n      products: 18,\n      lastActive: '1 day ago',\n      plan: 'Professional',\n      integration: 'Complete'\n    },\n    {\n      id: 3,\n      name: 'Fashion Accessories',\n      website: 'fashionaccessories.com',\n      email: '<EMAIL>',\n      status: 'active',\n      joinDate: '2024-01-28',\n      tryOns: 2340,\n      conversion: '16.5%',\n      revenue: 28900,\n      products: 32,\n      lastActive: '3 hours ago',\n      plan: 'Professional',\n      integration: 'Partial'\n    },\n    {\n      id: 4,\n      name: 'Premium Brands',\n      website: 'premiumbrands.com',\n      email: '<EMAIL>',\n      status: 'active',\n      joinDate: '2024-03-10',\n      tryOns: 1980,\n      conversion: '21.2%',\n      revenue: 32400,\n      products: 15,\n      lastActive: '5 hours ago',\n      plan: 'Premium',\n      integration: 'Complete'\n    },\n    {\n      id: 5,\n      name: 'Style Studio',\n      website: 'stylestudio.com',\n      email: '<EMAIL>',\n      status: 'pending',\n      joinDate: '2024-02-20',\n      tryOns: 1650,\n      conversion: '18.9%',\n      revenue: 24800,\n      products: 12,\n      lastActive: '2 weeks ago',\n      plan: 'Basic',\n      integration: 'Pending'\n    }\n  ];\n\n  const filteredClients = clients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         client.email.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesStatus = selectedStatus === 'all' || client.status === selectedStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const handleAddClient = (e) => {\n    e.preventDefault();\n    // Here you would handle adding the client (API call, etc.)\n    setShowModal(false);\n    setClientForm({ name: '', website: '', email: '', password: '' });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={() => setShowModal(true)}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+2 new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clients.filter(c => c.status === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">80%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clients.reduce((sum, c) => sum + c.tryOns, 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+15%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">${clients.reduce((sum, c) => sum + c.revenue, 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center\">\n                  <Globe className=\"h-6 w-6 text-orange-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+22%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add Client Modal */}\n          {showModal && (\n            <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n              <div className=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative\">\n                <button\n                  className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600\"\n                  onClick={() => setShowModal(false)}\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n                <h2 className=\"text-xl font-semibold mb-4\">Add Client</h2>\n                <form onSubmit={handleAddClient} className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Client Name</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={clientForm.name}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Website</label>\n                    <input\n                      type=\"text\"\n                      name=\"website\"\n                      value={clientForm.website}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={clientForm.email}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n                    <div className=\"flex gap-2\">\n                      <input\n                        type=\"text\"\n                        name=\"password\"\n                        value={clientForm.password}\n                        onChange={handleFormChange}\n                        required\n                        className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={handleSuggestPassword}\n                        className=\"mt-1 px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300\"\n                      >\n                        Suggest Password\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"flex justify-end\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                    >\n                      Add Client\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </div>\n          )}\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Conversion</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {filteredClients.map((client) => (\n                    <motion.tr\n                      key={client.id}\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"hover:bg-gray-50\"\n                    >\n                      <td className=\"px-4 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                              {client.name.charAt(0)}\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{client.name}</div>\n                            <div className=\"text-sm text-gray-500\">{client.email}</div>\n                            <div className=\"text-sm text-gray-500 lg:hidden\">\n                              {client.tryOns.toLocaleString()} try-ons • {client.conversion} conversion\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                        <div className=\"text-sm font-medium text-gray-900\">{client.tryOns.toLocaleString()}</div>\n                        <div className=\"text-sm text-gray-500\">{client.products} products</div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                        <div className=\"text-sm font-medium text-gray-900\">{client.conversion}</div>\n                        <div className=\"text-sm text-gray-500\">${client.revenue.toLocaleString()} revenue</div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                        <div className=\"flex flex-col space-y-1\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\n                          }`}>\n                            {client.status}\n                          </span>\n                          <span className=\"text-xs text-gray-500\">{client.lastActive}</span>\n                        </div>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          client.integration === 'Complete' ? 'bg-green-100 text-green-800' :\n                          client.integration === 'Partial' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'\n                        }`}>\n                          {client.integration}\n                        </span>\n                      </td>\n                      <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <div className=\"flex justify-end space-x-2\">\n                          <button className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-1\">\n                            <Eye className=\"h-4 w-4\" />\n                          </button>\n                          <button className=\"text-blue-600 hover:text-blue-800 p-1\">\n                            <Code className=\"h-4 w-4\" />\n                          </button>\n                          <button className=\"text-gray-600 hover:text-gray-800 p-1\">\n                            <Edit className=\"h-4 w-4\" />\n                          </button>\n                          <button className=\"text-red-600 hover:text-red-800 p-1\">\n                            <Trash2 className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </td>\n                    </motion.tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC;IAC3CsC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTvB,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1Bd,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMgB,UAAU,GAAGd,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA,MAAMe,OAAO,GAAG,CACd;IACEC,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE,2BAA2B;IAClCK,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,oBAAoB;IAC7BC,KAAK,EAAE,yBAAyB;IAChCK,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,WAAW;IACvBC,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,wBAAwB;IACjCC,KAAK,EAAE,8BAA8B;IACrCK,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE,2BAA2B;IAClCK,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,iBAAiB;IAC1BC,KAAK,EAAE,sBAAsB;IAC7BK,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,eAAe,GAAGX,OAAO,CAACY,MAAM,CAACC,MAAM,IAAI;IAC/C,MAAMC,aAAa,GAAGD,MAAM,CAAClB,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,WAAW,CAAC4B,WAAW,CAAC,CAAC,CAAC,IAC9DF,MAAM,CAAChB,KAAK,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,WAAW,CAAC4B,WAAW,CAAC,CAAC,CAAC;IACnF,MAAME,aAAa,GAAG5B,cAAc,KAAK,KAAK,IAAIwB,MAAM,CAACX,MAAM,KAAKb,cAAc;IAClF,OAAOyB,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAExB,IAAI;MAAEyB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC3B,aAAa,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC3B,IAAI,GAAGyB;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC7B,aAAa,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhD,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAMoD,eAAe,GAAIL,CAAC,IAAK;IAC7BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB;IACAjC,YAAY,CAAC,KAAK,CAAC;IACnBE,aAAa,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEvB,QAAQ,EAAE;IAAG,CAAC,CAAC;EACnE,CAAC;EAED,oBACEH,OAAA;IAAKuD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCxD,OAAA,CAACb,YAAY;MAACsE,MAAM,EAAE7C,aAAc;MAAC8C,OAAO,EAAEA,CAAA,KAAM7C,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjI9D,OAAA,CAACZ,WAAW;MAACuC,aAAa,EAAEA,aAAc;MAACb,SAAS,EAAEA;IAAU;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnE9D,OAAA;MAAMuD,SAAS,EAAE,GAAG3B,UAAU,oCAAqC;MAAA4B,QAAA,eACjExD,OAAA;QAAKuD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBxD,OAAA;UAAKuD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAIuD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE9D,OAAA;cAAGuD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACN9D,OAAA;YACEuD,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAAC,IAAI,CAAE;YAAAmC,QAAA,gBAElCxD,OAAA,CAACT,IAAI;cAACgE,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9D,OAAA;UAAKuD,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFxD,OAAA,CAACX,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxD,OAAA;cAAKuD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAGuD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClE9D,OAAA;kBAAGuD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE3B,OAAO,CAACpB;gBAAM;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACN9D,OAAA;gBAAKuD,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFxD,OAAA,CAACH,KAAK;kBAAC0D,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAMuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClE9D,OAAA;gBAAMuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb9D,OAAA,CAACX,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxD,OAAA;cAAKuD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAGuD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnE9D,OAAA;kBAAGuD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE3B,OAAO,CAACY,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAACtB;gBAAM;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC,eACN9D,OAAA;gBAAKuD,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFxD,OAAA,CAACJ,UAAU;kBAAC2D,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAMuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/D9D,OAAA;gBAAMuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb9D,OAAA,CAACX,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxD,OAAA;cAAKuD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAGuD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClE9D,OAAA;kBAAGuD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE3B,OAAO,CAAC2C,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACtC,MAAM,EAAE,CAAC,CAAC,CAACyC,cAAc,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H,CAAC,eACN9D,OAAA;gBAAKuD,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFxD,OAAA,CAACR,GAAG;kBAAC+D,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAMuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChE9D,OAAA;gBAAMuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb9D,OAAA,CAACX,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CxD,OAAA;cAAKuD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAGuD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClE9D,OAAA;kBAAGuD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAC,GAAC,EAAC3B,OAAO,CAAC2C,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACpC,OAAO,EAAE,CAAC,CAAC,CAACuC,cAAc,CAAC,CAAC;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5H,CAAC,eACN9D,OAAA;gBAAKuD,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvFxD,OAAA,CAACL,KAAK;kBAAC4D,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9D,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAMuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChE9D,OAAA;gBAAMuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGL1C,SAAS,iBACRpB,OAAA;UAAKuD,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzFxD,OAAA;YAAKuD,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzExD,OAAA;cACEuD,SAAS,EAAC,0DAA0D;cACpEQ,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAAC,KAAK,CAAE;cAAAmC,QAAA,eAEnCxD,OAAA;gBAAK2E,KAAK,EAAC,4BAA4B;gBAACpB,SAAS,EAAC,SAAS;gBAACqB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAtB,QAAA,eAC/GxD,OAAA;kBAAM+E,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACT9D,OAAA;cAAIuD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1D9D,OAAA;cAAMmF,QAAQ,EAAE9B,eAAgB;cAACE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACpDxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E9D,OAAA;kBACEoF,IAAI,EAAC,MAAM;kBACX5D,IAAI,EAAC,MAAM;kBACXyB,KAAK,EAAE3B,UAAU,CAACE,IAAK;kBACvB6D,QAAQ,EAAEtC,gBAAiB;kBAC3BuC,QAAQ;kBACR/B,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1E9D,OAAA;kBACEoF,IAAI,EAAC,MAAM;kBACX5D,IAAI,EAAC,SAAS;kBACdyB,KAAK,EAAE3B,UAAU,CAACG,OAAQ;kBAC1B4D,QAAQ,EAAEtC,gBAAiB;kBAC3BuC,QAAQ;kBACR/B,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxE9D,OAAA;kBACEoF,IAAI,EAAC,OAAO;kBACZ5D,IAAI,EAAC,OAAO;kBACZyB,KAAK,EAAE3B,UAAU,CAACI,KAAM;kBACxB2D,QAAQ,EAAEtC,gBAAiB;kBAC3BuC,QAAQ;kBACR/B,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3E9D,OAAA;kBAAKuD,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBxD,OAAA;oBACEoF,IAAI,EAAC,MAAM;oBACX5D,IAAI,EAAC,UAAU;oBACfyB,KAAK,EAAE3B,UAAU,CAACnB,QAAS;oBAC3BkF,QAAQ,EAAEtC,gBAAiB;oBAC3BuC,QAAQ;oBACR/B,SAAS,EAAC;kBAA+G;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC,eACF9D,OAAA;oBACEoF,IAAI,EAAC,QAAQ;oBACbrB,OAAO,EAAEX,qBAAsB;oBAC/BG,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EACzE;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9D,OAAA;gBAAKuD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BxD,OAAA;kBACEoF,IAAI,EAAC,QAAQ;kBACb7B,SAAS,EAAC,6NAA6N;kBAAAC,QAAA,EACxO;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD9D,OAAA;UAAKuD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDxD,OAAA;YAAKuD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CxD,OAAA;cAAKuD,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBxD,OAAA;gBACEoF,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/BtC,KAAK,EAAEjC,WAAY;gBACnBqE,QAAQ,EAAGrC,CAAC,IAAK/B,cAAc,CAAC+B,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;gBAChDM,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9D,OAAA;cAAKuD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BxD,OAAA;gBACEiD,KAAK,EAAE/B,cAAe;gBACtBmE,QAAQ,EAAGrC,CAAC,IAAK7B,iBAAiB,CAAC6B,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;gBACnDM,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5IxD,OAAA;kBAAQiD,KAAK,EAAC,KAAK;kBAAAO,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC9D,OAAA;kBAAQiD,KAAK,EAAC,QAAQ;kBAAAO,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC9D,OAAA;kBAAQiD,KAAK,EAAC,SAAS;kBAAAO,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9D,OAAA;UAAKuD,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DxD,OAAA;YAAKuD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BxD,OAAA;cAAOuD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpDxD,OAAA;gBAAOuD,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3BxD,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAIuD,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1G9D,OAAA;oBAAIuD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChI9D,OAAA;oBAAIuD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnI9D,OAAA;oBAAIuD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/H9D,OAAA;oBAAIuD,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpI9D,OAAA;oBAAIuD,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR9D,OAAA;gBAAOuD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDhB,eAAe,CAACgD,GAAG,CAAE9C,MAAM,iBAC1B1C,OAAA,CAACX,MAAM,CAACoG,EAAE;kBAERxB,OAAO,EAAE;oBAAEC,OAAO,EAAE;kBAAE,CAAE;kBACxBE,OAAO,EAAE;oBAAEF,OAAO,EAAE;kBAAE,CAAE;kBACxBX,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAE5BxD,OAAA;oBAAIuD,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzCxD,OAAA;sBAAKuD,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCxD,OAAA;wBAAKuD,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,eACtCxD,OAAA;0BAAKuD,SAAS,EAAC,iFAAiF;0BAAAC,QAAA,EAC7Fd,MAAM,CAAClB,IAAI,CAACnB,MAAM,CAAC,CAAC;wBAAC;0BAAAsD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9D,OAAA;wBAAKuD,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBxD,OAAA;0BAAKuD,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAEd,MAAM,CAAClB;wBAAI;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtE9D,OAAA;0BAAKuD,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAEd,MAAM,CAAChB;wBAAK;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3D9D,OAAA;0BAAKuD,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,GAC7Cd,MAAM,CAACT,MAAM,CAACyC,cAAc,CAAC,CAAC,EAAC,kBAAW,EAAChC,MAAM,CAACR,UAAU,EAAC,aAChE;wBAAA;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL9D,OAAA;oBAAIuD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBAC9DxD,OAAA;sBAAKuD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEd,MAAM,CAACT,MAAM,CAACyC,cAAc,CAAC;oBAAC;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzF9D,OAAA;sBAAKuD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAEd,MAAM,CAACN,QAAQ,EAAC,WAAS;oBAAA;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACL9D,OAAA;oBAAIuD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBAC9DxD,OAAA;sBAAKuD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEd,MAAM,CAACR;oBAAU;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5E9D,OAAA;sBAAKuD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAACd,MAAM,CAACP,OAAO,CAACuC,cAAc,CAAC,CAAC,EAAC,UAAQ;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eACL9D,OAAA;oBAAIuD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC9DxD,OAAA;sBAAKuD,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACtCxD,OAAA;wBAAMuD,SAAS,EAAE,2EACfb,MAAM,CAACX,MAAM,KAAK,QAAQ,GAAG,6BAA6B,GAAG,+BAA+B,EAC3F;wBAAAyB,QAAA,EACAd,MAAM,CAACX;sBAAM;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACP9D,OAAA;wBAAMuD,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEd,MAAM,CAACL;sBAAU;wBAAAsB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL9D,OAAA;oBAAIuD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC9DxD,OAAA;sBAAMuD,SAAS,EAAE,2EACfb,MAAM,CAACH,WAAW,KAAK,UAAU,GAAG,6BAA6B,GACjEG,MAAM,CAACH,WAAW,KAAK,SAAS,GAAG,+BAA+B,GAAG,yBAAyB,EAC7F;sBAAAiB,QAAA,EACAd,MAAM,CAACH;oBAAW;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL9D,OAAA;oBAAIuD,SAAS,EAAC,4DAA4D;oBAAAC,QAAA,eACxExD,OAAA;sBAAKuD,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,gBACzCxD,OAAA;wBAAQuD,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,eAC5DxD,OAAA,CAACR,GAAG;0BAAC+D,SAAS,EAAC;wBAAS;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC,eACT9D,OAAA;wBAAQuD,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,eACvDxD,OAAA,CAACF,IAAI;0BAACyD,SAAS,EAAC;wBAAS;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACT9D,OAAA;wBAAQuD,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,eACvDxD,OAAA,CAACP,IAAI;0BAAC8D,SAAS,EAAC;wBAAS;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACT9D,OAAA;wBAAQuD,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eACrDxD,OAAA,CAACN,MAAM;0BAAC6D,SAAS,EAAC;wBAAS;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GA9DApB,MAAM,CAACZ,EAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+DL,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnD,EAAA,CA3aID,OAAO;AAAAgF,EAAA,GAAPhF,OAAO;AA6ab,eAAeA,OAAO;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}