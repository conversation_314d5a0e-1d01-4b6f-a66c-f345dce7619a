{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\ClientSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Bell, Shield, Globe, Code, Eye, EyeOff } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientSettings = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [showApiKey, setShowApiKey] = useState(false);\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n  const [settings, setSettings] = useState({\n    // Profile Settings\n    companyName: 'Luxury Watches Co.',\n    contactEmail: '<EMAIL>',\n    website: 'https://luxurywatches.com',\n    phone: '+****************',\n    // Notification Settings\n    emailNotifications: true,\n    tryOnAlerts: true,\n    weeklyReports: true,\n    marketingEmails: false,\n    // Integration Settings\n    apiKey: 'lw_live_sk_1234567890abcdef',\n    webhookUrl: 'https://luxurywatches.com/webhooks/viatryon',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    // Privacy Settings\n    dataRetention: '12',\n    analyticsSharing: true,\n    publicProfile: false\n  });\n  const handleInputChange = (field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSave = () => {\n    // Handle save functionality\n    console.log('Saving settings:', settings);\n  };\n  const tabs = [{\n    id: 'profile',\n    label: 'Profile',\n    icon: User\n  }, {\n    id: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }, {\n    id: 'integration',\n    label: 'Integration',\n    icon: Code\n  }, {\n    id: 'privacy',\n    label: 'Privacy',\n    icon: Shield\n  }];\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Company Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Company Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: settings.companyName,\n                  onChange: e => handleInputChange('companyName', e.target.value),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Contact Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: settings.contactEmail,\n                  onChange: e => handleInputChange('contactEmail', e.target.value),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Website URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: settings.website,\n                  onChange: e => handleInputChange('website', e.target.value),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: settings.phone,\n                  onChange: e => handleInputChange('phone', e.target.value),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this);\n      case 'notifications':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Notification Preferences\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Email Notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Receive general email notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"relative inline-flex items-center cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: settings.emailNotifications,\n                    onChange: e => handleInputChange('emailNotifications', e.target.checked),\n                    className: \"sr-only peer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Try-On Alerts\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Get notified about high try-on activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"relative inline-flex items-center cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: settings.tryOnAlerts,\n                    onChange: e => handleInputChange('tryOnAlerts', e.target.checked),\n                    className: \"sr-only peer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Weekly Reports\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Receive weekly analytics reports\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"relative inline-flex items-center cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: settings.weeklyReports,\n                    onChange: e => handleInputChange('weeklyReports', e.target.checked),\n                    className: \"sr-only peer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Marketing Emails\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Receive product updates and marketing content\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"relative inline-flex items-center cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: settings.marketingEmails,\n                    onChange: e => handleInputChange('marketingEmails', e.target.checked),\n                    className: \"sr-only peer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this);\n      case 'integration':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"API & Integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"API Key\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: showApiKey ? 'text' : 'password',\n                    value: settings.apiKey,\n                    readOnly: true,\n                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg bg-gray-50 focus:outline-none\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowApiKey(!showApiKey),\n                    className: \"px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 hover:bg-gray-100\",\n                    children: showApiKey ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 37\n                    }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 70\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Use this API key to authenticate your integration requests\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Webhook URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: settings.webhookUrl,\n                  onChange: e => handleInputChange('webhookUrl', e.target.value),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"We'll send try-on events to this URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Default Button Style\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: settings.buttonStyle,\n                    onChange: e => handleInputChange('buttonStyle', e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"primary\",\n                      children: \"Primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"secondary\",\n                      children: \"Secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"outline\",\n                      children: \"Outline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"minimal\",\n                      children: \"Minimal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Default Button Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: settings.buttonSize,\n                    onChange: e => handleInputChange('buttonSize', e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"small\",\n                      children: \"Small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"medium\",\n                      children: \"Medium\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"large\",\n                      children: \"Large\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this);\n      case 'privacy':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Privacy & Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Data Retention Period (months)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: settings.dataRetention,\n                  onChange: e => handleInputChange('dataRetention', e.target.value),\n                  className: \"w-full md:w-48 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"3\",\n                    children: \"3 months\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"6\",\n                    children: \"6 months\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"12\",\n                    children: \"12 months\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"24\",\n                    children: \"24 months\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"indefinite\",\n                    children: \"Indefinite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Analytics Sharing\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Share anonymized analytics to improve the platform\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"relative inline-flex items-center cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: settings.analyticsSharing,\n                    onChange: e => handleInputChange('analyticsSharing', e.target.checked),\n                    className: \"sr-only peer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"Public Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Make your company profile visible in our directory\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"relative inline-flex items-center cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: settings.publicProfile,\n                    onChange: e => handleInputChange('publicProfile', e.target.checked),\n                    className: \"sr-only peer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-20 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your account preferences and integration settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex space-x-8 px-6\",\n              \"aria-label\": \"Tabs\",\n              children: tabs.map(tab => {\n                const Icon = tab.icon;\n                return /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab(tab.id),\n                  className: `py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === tab.id ? 'border-[#2D8C88] text-[#2D8C88]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: tab.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 23\n                  }, this)]\n                }, tab.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: renderTabContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n              children: [/*#__PURE__*/_jsxDEV(Save, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this), \"Save Changes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 340,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientSettings, \"+qc2W5TkKawtwuhsJxTugYGNdL0=\");\n_c = ClientSettings;\nexport default ClientSettings;\nvar _c;\n$RefreshReg$(_c, \"ClientSettings\");", "map": {"version": 3, "names": ["React", "useState", "ClientSidebar", "ClientNavbar", "motion", "Save", "User", "Bell", "Shield", "Globe", "Code", "Eye", "Eye<PERSON>ff", "jsxDEV", "_jsxDEV", "ClientSettings", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "activeTab", "setActiveTab", "showApiKey", "setShowApiKey", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "settings", "setSettings", "companyName", "contactEmail", "website", "phone", "emailNotifications", "tryOnAlerts", "weeklyReports", "marketingEmails", "<PERSON><PERSON><PERSON><PERSON>", "webhookUrl", "buttonStyle", "buttonSize", "dataRetention", "analyticsSharing", "publicProfile", "handleInputChange", "field", "value", "prev", "handleSave", "console", "log", "tabs", "id", "label", "icon", "renderTabContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "e", "target", "checked", "readOnly", "onClick", "isOpen", "onClose", "map", "tab", "Icon", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/ClientSettings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Bell, Shield, Globe, Code, Eye, EyeOff } from 'lucide-react';\n\nconst ClientSettings = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [showApiKey, setShowApiKey] = useState(false);\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  const [settings, setSettings] = useState({\n    // Profile Settings\n    companyName: 'Luxury Watches Co.',\n    contactEmail: '<EMAIL>',\n    website: 'https://luxurywatches.com',\n    phone: '+****************',\n    \n    // Notification Settings\n    emailNotifications: true,\n    tryOnAlerts: true,\n    weeklyReports: true,\n    marketingEmails: false,\n    \n    // Integration Settings\n    apiKey: 'lw_live_sk_1234567890abcdef',\n    webhookUrl: 'https://luxurywatches.com/webhooks/viatryon',\n    buttonStyle: 'primary',\n    buttonSize: 'medium',\n    \n    // Privacy Settings\n    dataRetention: '12',\n    analyticsSharing: true,\n    publicProfile: false\n  });\n\n  const handleInputChange = (field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSave = () => {\n    // Handle save functionality\n    console.log('Saving settings:', settings);\n  };\n\n  const tabs = [\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'integration', label: 'Integration', icon: Code },\n    { id: 'privacy', label: 'Privacy', icon: Shield }\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Company Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Company Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={settings.companyName}\n                    onChange={(e) => handleInputChange('companyName', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Contact Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={settings.contactEmail}\n                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Website URL\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={settings.website}\n                    onChange={(e) => handleInputChange('website', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Phone Number\n                  </label>\n                  <input\n                    type=\"tel\"\n                    value={settings.phone}\n                    onChange={(e) => handleInputChange('phone', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'notifications':\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Notification Preferences</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">Email Notifications</h4>\n                    <p className=\"text-sm text-gray-500\">Receive general email notifications</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.emailNotifications}\n                      onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"></div>\n                  </label>\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">Try-On Alerts</h4>\n                    <p className=\"text-sm text-gray-500\">Get notified about high try-on activity</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.tryOnAlerts}\n                      onChange={(e) => handleInputChange('tryOnAlerts', e.target.checked)}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">Weekly Reports</h4>\n                    <p className=\"text-sm text-gray-500\">Receive weekly analytics reports</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.weeklyReports}\n                      onChange={(e) => handleInputChange('weeklyReports', e.target.checked)}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">Marketing Emails</h4>\n                    <p className=\"text-sm text-gray-500\">Receive product updates and marketing content</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.marketingEmails}\n                      onChange={(e) => handleInputChange('marketingEmails', e.target.checked)}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"></div>\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'integration':\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">API & Integration</h3>\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    API Key\n                  </label>\n                  <div className=\"flex\">\n                    <input\n                      type={showApiKey ? 'text' : 'password'}\n                      value={settings.apiKey}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg bg-gray-50 focus:outline-none\"\n                    />\n                    <button\n                      onClick={() => setShowApiKey(!showApiKey)}\n                      className=\"px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 hover:bg-gray-100\"\n                    >\n                      {showApiKey ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                    </button>\n                  </div>\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Use this API key to authenticate your integration requests\n                  </p>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Webhook URL\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={settings.webhookUrl}\n                    onChange={(e) => handleInputChange('webhookUrl', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    We'll send try-on events to this URL\n                  </p>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Default Button Style\n                    </label>\n                    <select\n                      value={settings.buttonStyle}\n                      onChange={(e) => handleInputChange('buttonStyle', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    >\n                      <option value=\"primary\">Primary</option>\n                      <option value=\"secondary\">Secondary</option>\n                      <option value=\"outline\">Outline</option>\n                      <option value=\"minimal\">Minimal</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Default Button Size\n                    </label>\n                    <select\n                      value={settings.buttonSize}\n                      onChange={(e) => handleInputChange('buttonSize', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    >\n                      <option value=\"small\">Small</option>\n                      <option value=\"medium\">Medium</option>\n                      <option value=\"large\">Large</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'privacy':\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Privacy & Data</h3>\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Data Retention Period (months)\n                  </label>\n                  <select\n                    value={settings.dataRetention}\n                    onChange={(e) => handleInputChange('dataRetention', e.target.value)}\n                    className=\"w-full md:w-48 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  >\n                    <option value=\"3\">3 months</option>\n                    <option value=\"6\">6 months</option>\n                    <option value=\"12\">12 months</option>\n                    <option value=\"24\">24 months</option>\n                    <option value=\"indefinite\">Indefinite</option>\n                  </select>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">Analytics Sharing</h4>\n                    <p className=\"text-sm text-gray-500\">Share anonymized analytics to improve the platform</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.analyticsSharing}\n                      onChange={(e) => handleInputChange('analyticsSharing', e.target.checked)}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900\">Public Profile</h4>\n                    <p className=\"text-sm text-gray-500\">Make your company profile visible in our directory</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.publicProfile}\n                      onChange={(e) => handleInputChange('publicProfile', e.target.checked)}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#2D8C88]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#2D8C88]\"></div>\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-20 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Settings</h1>\n            <p className=\"text-gray-600\">Manage your account preferences and integration settings</p>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            {/* Tabs */}\n            <div className=\"border-b border-gray-200\">\n              <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n                {tabs.map((tab) => {\n                  const Icon = tab.icon;\n                  return (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                        activeTab === tab.id\n                          ? 'border-[#2D8C88] text-[#2D8C88]'\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      <span>{tab.label}</span>\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n\n            {/* Tab Content */}\n            <div className=\"p-6\">\n              {renderTabContent()}\n            </div>\n\n            {/* Save Button */}\n            <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end\">\n              <button\n                onClick={handleSave}\n                className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              >\n                <Save className=\"h-4 w-4 mr-2\" />\n                Save Changes\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default ClientSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElF,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1BP,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMS,UAAU,GAAGP,SAAS,GAAG,cAAc,GAAG,eAAe;EAE/D,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC;IACvC;IACA4B,WAAW,EAAE,oBAAoB;IACjCC,YAAY,EAAE,2BAA2B;IACzCC,OAAO,EAAE,2BAA2B;IACpCC,KAAK,EAAE,mBAAmB;IAE1B;IACAC,kBAAkB,EAAE,IAAI;IACxBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,KAAK;IAEtB;IACAC,MAAM,EAAE,6BAA6B;IACrCC,UAAU,EAAE,6CAA6C;IACzDC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,QAAQ;IAEpB;IACAC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1ClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB;IACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEvB,QAAQ,CAAC;EAC3C,CAAC;EAED,MAAMwB,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEhD;EAAK,CAAC,EAC/C;IAAE8C,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE/C;EAAK,CAAC,EAC3D;IAAE6C,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE5C;EAAK,CAAC,EACvD;IAAE0C,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE9C;EAAO,CAAC,CAClD;EAED,MAAM+C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQlC,SAAS;MACf,KAAK,SAAS;QACZ,oBACEP,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAI0C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E/C,OAAA;cAAK0C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD3C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBACEgD,IAAI,EAAC,MAAM;kBACXhB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;kBAC5BkC,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,aAAa,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAClEU,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBACEgD,IAAI,EAAC,OAAO;kBACZhB,KAAK,EAAEnB,QAAQ,CAACG,YAAa;kBAC7BiC,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,cAAc,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBACnEU,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBACEgD,IAAI,EAAC,KAAK;kBACVhB,KAAK,EAAEnB,QAAQ,CAACI,OAAQ;kBACxBgC,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,SAAS,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAC9DU,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBACEgD,IAAI,EAAC,KAAK;kBACVhB,KAAK,EAAEnB,QAAQ,CAACK,KAAM;kBACtB+B,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,OAAO,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAC5DU,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,eAAe;QAClB,oBACE/C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAI0C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF/C,OAAA;cAAK0C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3C,OAAA;gBAAK0C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAI0C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1E/C,OAAA;oBAAG0C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAmC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACN/C,OAAA;kBAAO0C,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBACjE3C,OAAA;oBACEgD,IAAI,EAAC,UAAU;oBACfI,OAAO,EAAEvC,QAAQ,CAACM,kBAAmB;oBACrC8B,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,oBAAoB,EAAEoB,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE;oBAC3EV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACF/C,OAAA;oBAAK0C,SAAS,EAAC;kBAA8X;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/Y,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAI0C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpE/C,OAAA;oBAAG0C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACN/C,OAAA;kBAAO0C,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBACjE3C,OAAA;oBACEgD,IAAI,EAAC,UAAU;oBACfI,OAAO,EAAEvC,QAAQ,CAACO,WAAY;oBAC9B6B,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,aAAa,EAAEoB,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE;oBACpEV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACF/C,OAAA;oBAAK0C,SAAS,EAAC;kBAA8X;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/Y,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAI0C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrE/C,OAAA;oBAAG0C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACN/C,OAAA;kBAAO0C,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBACjE3C,OAAA;oBACEgD,IAAI,EAAC,UAAU;oBACfI,OAAO,EAAEvC,QAAQ,CAACQ,aAAc;oBAChC4B,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,eAAe,EAAEoB,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE;oBACtEV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACF/C,OAAA;oBAAK0C,SAAS,EAAC;kBAA8X;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/Y,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAI0C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvE/C,OAAA;oBAAG0C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA6C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACN/C,OAAA;kBAAO0C,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBACjE3C,OAAA;oBACEgD,IAAI,EAAC,UAAU;oBACfI,OAAO,EAAEvC,QAAQ,CAACS,eAAgB;oBAClC2B,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,iBAAiB,EAAEoB,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE;oBACxEV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACF/C,OAAA;oBAAK0C,SAAS,EAAC;kBAA8X;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/Y,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,aAAa;QAChB,oBACE/C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAI0C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7E/C,OAAA;cAAK0C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBAAK0C,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB3C,OAAA;oBACEgD,IAAI,EAAEvC,UAAU,GAAG,MAAM,GAAG,UAAW;oBACvCuB,KAAK,EAAEnB,QAAQ,CAACU,MAAO;oBACvB8B,QAAQ;oBACRX,SAAS,EAAC;kBAAoF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/F,CAAC,eACF/C,OAAA;oBACEsD,OAAO,EAAEA,CAAA,KAAM5C,aAAa,CAAC,CAACD,UAAU,CAAE;oBAC1CiC,SAAS,EAAC,uFAAuF;oBAAAC,QAAA,EAEhGlC,UAAU,gBAAGT,OAAA,CAACF,MAAM;sBAAC4C,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG/C,OAAA,CAACH,GAAG;sBAAC6C,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN/C,OAAA;kBAAG0C,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN/C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBACEgD,IAAI,EAAC,KAAK;kBACVhB,KAAK,EAAEnB,QAAQ,CAACW,UAAW;kBAC3ByB,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,YAAY,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBACjEU,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC,eACF/C,OAAA;kBAAG0C,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAO0C,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR/C,OAAA;oBACEgC,KAAK,EAAEnB,QAAQ,CAACY,WAAY;oBAC5BwB,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,aAAa,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;oBAClEU,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5I3C,OAAA;sBAAQgC,KAAK,EAAC,SAAS;sBAAAW,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC/C,OAAA;sBAAQgC,KAAK,EAAC,WAAW;sBAAAW,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C/C,OAAA;sBAAQgC,KAAK,EAAC,SAAS;sBAAAW,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC/C,OAAA;sBAAQgC,KAAK,EAAC,SAAS;sBAAAW,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN/C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAO0C,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR/C,OAAA;oBACEgC,KAAK,EAAEnB,QAAQ,CAACa,UAAW;oBAC3BuB,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,YAAY,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;oBACjEU,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5I3C,OAAA;sBAAQgC,KAAK,EAAC,OAAO;sBAAAW,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpC/C,OAAA;sBAAQgC,KAAK,EAAC,QAAQ;sBAAAW,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC/C,OAAA;sBAAQgC,KAAK,EAAC,OAAO;sBAAAW,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,SAAS;QACZ,oBACE/C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAI0C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E/C,OAAA;cAAK0C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBACEgC,KAAK,EAAEnB,QAAQ,CAACc,aAAc;kBAC9BsB,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,eAAe,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBACpEU,SAAS,EAAC,0IAA0I;kBAAAC,QAAA,gBAEpJ3C,OAAA;oBAAQgC,KAAK,EAAC,GAAG;oBAAAW,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnC/C,OAAA;oBAAQgC,KAAK,EAAC,GAAG;oBAAAW,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnC/C,OAAA;oBAAQgC,KAAK,EAAC,IAAI;oBAAAW,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC/C,OAAA;oBAAQgC,KAAK,EAAC,IAAI;oBAAAW,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC/C,OAAA;oBAAQgC,KAAK,EAAC,YAAY;oBAAAW,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAI0C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxE/C,OAAA;oBAAG0C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAkD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eACN/C,OAAA;kBAAO0C,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBACjE3C,OAAA;oBACEgD,IAAI,EAAC,UAAU;oBACfI,OAAO,EAAEvC,QAAQ,CAACe,gBAAiB;oBACnCqB,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,kBAAkB,EAAEoB,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE;oBACzEV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACF/C,OAAA;oBAAK0C,SAAS,EAAC;kBAA8X;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/Y,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD3C,OAAA;kBAAA2C,QAAA,gBACE3C,OAAA;oBAAI0C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrE/C,OAAA;oBAAG0C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAkD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eACN/C,OAAA;kBAAO0C,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBACjE3C,OAAA;oBACEgD,IAAI,EAAC,UAAU;oBACfI,OAAO,EAAEvC,QAAQ,CAACgB,aAAc;oBAChCoB,QAAQ,EAAGC,CAAC,IAAKpB,iBAAiB,CAAC,eAAe,EAAEoB,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE;oBACtEV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACF/C,OAAA;oBAAK0C,SAAS,EAAC;kBAA8X;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/Y,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE/C,OAAA;IAAK0C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC3C,OAAA,CAACZ,aAAa;MAACmE,MAAM,EAAEpD,aAAc;MAACqD,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClI/C,OAAA,CAACX,YAAY;MAACsB,aAAa,EAAEA,aAAc;MAACN,SAAS,EAAEA;IAAU;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpE/C,OAAA;MAAM0C,SAAS,EAAE,GAAG9B,UAAU,oCAAqC;MAAA+B,QAAA,eACjE3C,OAAA;QAAK0C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzB3C,OAAA;UAAK0C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB3C,OAAA;YAAI0C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D/C,OAAA;YAAG0C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC,eAEN/C,OAAA;UAAK0C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAE5D3C,OAAA;YAAK0C,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvC3C,OAAA;cAAK0C,SAAS,EAAC,qBAAqB;cAAC,cAAW,MAAM;cAAAC,QAAA,EACnDN,IAAI,CAACoB,GAAG,CAAEC,GAAG,IAAK;gBACjB,MAAMC,IAAI,GAAGD,GAAG,CAAClB,IAAI;gBACrB,oBACExC,OAAA;kBAEEsD,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACkD,GAAG,CAACpB,EAAE,CAAE;kBACpCI,SAAS,EAAE,wEACTnC,SAAS,KAAKmD,GAAG,CAACpB,EAAE,GAChB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAK,QAAA,gBAEH3C,OAAA,CAAC2D,IAAI;oBAACjB,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5B/C,OAAA;oBAAA2C,QAAA,EAAOe,GAAG,CAACnB;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GATnBW,GAAG,CAACpB,EAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUL,CAAC;cAEb,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/C,OAAA;YAAK0C,SAAS,EAAC,KAAK;YAAAC,QAAA,EACjBF,gBAAgB,CAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAGN/C,OAAA;YAAK0C,SAAS,EAAC,gEAAgE;YAAAC,QAAA,eAC7E3C,OAAA;cACEsD,OAAO,EAAEpB,UAAW;cACpBQ,SAAS,EAAC,6KAA6K;cAAAC,QAAA,gBAEvL3C,OAAA,CAACT,IAAI;gBAACmD,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAtYID,cAAc;AAAA2D,EAAA,GAAd3D,cAAc;AAwYpB,eAAeA,cAAc;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}