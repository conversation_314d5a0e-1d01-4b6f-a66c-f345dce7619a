{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\admin\\\\Settings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Bell, Shield, Globe, Database, Mail, Key, Server } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [activeTab, setActiveTab] = useState('general');\n  const [formData, setFormData] = useState({\n    companyName: 'ViaTryon',\n    email: '<EMAIL>',\n    notifications: true,\n    darkMode: false,\n    language: 'en',\n    timezone: 'UTC'\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Handle form submission\n    console.log('Form submitted:', formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-20 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Platform Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your virtual try-on platform configuration and preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex flex-wrap md:flex-nowrap space-x-0 md:space-x-8 px-4 md:px-6\",\n              \"aria-label\": \"Tabs\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('general'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'general' ? 'border-[#2D8C88] text-[#2D8C88]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: \"General\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('notifications'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'notifications' ? 'border-[#2D8C88] text-[#2D8C88]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('security'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'security' ? 'border-[#2D8C88] text-[#2D8C88]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: \"Security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('billing'),\n                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${activeTab === 'billing' ? 'border-[#2D8C88] text-[#2D8C88]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                children: \"Billing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 md:p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              children: [activeTab === 'general' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Profile Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Update your account's profile information.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"name\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      id: \"name\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\",\n                      defaultValue: \"Admin User\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"email\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      id: \"email\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\",\n                      defaultValue: \"<EMAIL>\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), activeTab === 'notifications' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Notification Preferences\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Manage how you receive notifications.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"Email Notifications\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Receive notifications via email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 144,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 142,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 bg-[#2D8C88]\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 150,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"Push Notifications\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 155,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Receive push notifications\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 bg-gray-200\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 162,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), activeTab === 'security' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Security Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Manage your account security settings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"current-password\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"Current Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      id: \"current-password\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"new-password\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"New Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      id: \"new-password\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"confirm-password\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"Confirm New Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      id: \"confirm-password\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), activeTab === 'billing' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"Billing Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Manage your billing information and subscription.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"card-number\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"Card Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      id: \"card-number\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\",\n                      placeholder: \"**** **** **** ****\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: \"expiry\",\n                      className: \"block text-sm font-medium text-gray-700\",\n                      children: \"Expiry Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      id: \"expiry\",\n                      className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\",\n                      placeholder: \"MM/YY\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 flex justify-end\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: \"Save Changes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"TgnMDeRg0hbLP8XsE9f8bJxfcPg=\");\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "Save", "User", "Bell", "Shield", "Globe", "Database", "Mail", "Key", "Server", "jsxDEV", "_jsxDEV", "Settings", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "activeTab", "setActiveTab", "formData", "setFormData", "companyName", "email", "notifications", "darkMode", "language", "timezone", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "handleSubmit", "preventDefault", "console", "log", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "id", "defaultValue", "placeholder", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/admin/Settings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Bell, Shield, Globe, Database, Mail, Key, Server } from 'lucide-react';\n\nconst Settings = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [activeTab, setActiveTab] = useState('general');\n  const [formData, setFormData] = useState({\n    companyName: 'ViaTryon',\n    email: '<EMAIL>',\n    notifications: true,\n    darkMode: false,\n    language: 'en',\n    timezone: 'UTC',\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // Handle form submission\n    console.log('Form submitted:', formData);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-20 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6 space-y-6\">\n          {/* Page Header */}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Platform Settings</h1>\n            <p className=\"text-gray-600\">Manage your virtual try-on platform configuration and preferences</p>\n          </div>\n\n          {/* Settings Tabs */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"border-b border-gray-200\">\n              <nav className=\"flex flex-wrap md:flex-nowrap space-x-0 md:space-x-8 px-4 md:px-6\" aria-label=\"Tabs\">\n                <button\n                  onClick={() => setActiveTab('general')}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                    activeTab === 'general'\n                      ? 'border-[#2D8C88] text-[#2D8C88]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  General\n                </button>\n                <button\n                  onClick={() => setActiveTab('notifications')}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                    activeTab === 'notifications'\n                      ? 'border-[#2D8C88] text-[#2D8C88]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  Notifications\n                </button>\n                <button\n                  onClick={() => setActiveTab('security')}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                    activeTab === 'security'\n                      ? 'border-[#2D8C88] text-[#2D8C88]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  Security\n                </button>\n                <button\n                  onClick={() => setActiveTab('billing')}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                    activeTab === 'billing'\n                      ? 'border-[#2D8C88] text-[#2D8C88]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  Billing\n                </button>\n              </nav>\n            </div>\n\n            {/* Settings Content */}\n            <div className=\"p-4 md:p-6\">\n              <form onSubmit={handleSubmit}>\n                {activeTab === 'general' && (\n                  <div className=\"space-y-6\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">Profile Information</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">Update your account's profile information.</p>\n                    </div>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">Name</label>\n                        <input\n                          type=\"text\"\n                          id=\"name\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                          defaultValue=\"Admin User\"\n                        />\n                      </div>\n                      <div>\n                        <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">Email</label>\n                        <input\n                          type=\"email\"\n                          id=\"email\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                          defaultValue=\"<EMAIL>\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {activeTab === 'notifications' && (\n                  <div className=\"space-y-6\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">Notification Preferences</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">Manage how you receive notifications.</p>\n                    </div>\n                    <div className=\"space-y-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"text-sm font-medium text-gray-900\">Email Notifications</h4>\n                          <p className=\"text-sm text-gray-500\">Receive notifications via email</p>\n                        </div>\n                        <button\n                          type=\"button\"\n                          className=\"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 bg-[#2D8C88]\"\n                        >\n                          <span className=\"translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out\" />\n                        </button>\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"text-sm font-medium text-gray-900\">Push Notifications</h4>\n                          <p className=\"text-sm text-gray-500\">Receive push notifications</p>\n                        </div>\n                        <button\n                          type=\"button\"\n                          className=\"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 bg-gray-200\"\n                        >\n                          <span className=\"translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {activeTab === 'security' && (\n                  <div className=\"space-y-6\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">Security Settings</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">Manage your account security settings.</p>\n                    </div>\n                    <div className=\"space-y-4\">\n                      <div>\n                        <label htmlFor=\"current-password\" className=\"block text-sm font-medium text-gray-700\">Current Password</label>\n                        <input\n                          type=\"password\"\n                          id=\"current-password\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                        />\n                      </div>\n                      <div>\n                        <label htmlFor=\"new-password\" className=\"block text-sm font-medium text-gray-700\">New Password</label>\n                        <input\n                          type=\"password\"\n                          id=\"new-password\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                        />\n                      </div>\n                      <div>\n                        <label htmlFor=\"confirm-password\" className=\"block text-sm font-medium text-gray-700\">Confirm New Password</label>\n                        <input\n                          type=\"password\"\n                          id=\"confirm-password\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {activeTab === 'billing' && (\n                  <div className=\"space-y-6\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">Billing Information</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">Manage your billing information and subscription.</p>\n                    </div>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label htmlFor=\"card-number\" className=\"block text-sm font-medium text-gray-700\">Card Number</label>\n                        <input\n                          type=\"text\"\n                          id=\"card-number\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                          placeholder=\"**** **** **** ****\"\n                        />\n                      </div>\n                      <div>\n                        <label htmlFor=\"expiry\" className=\"block text-sm font-medium text-gray-700\">Expiry Date</label>\n                        <input\n                          type=\"text\"\n                          id=\"expiry\"\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                          placeholder=\"MM/YY\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Save Button */}\n                <div className=\"mt-6 flex justify-end\">\n                  <button\n                    type=\"button\"\n                    className=\"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                  >\n                    Save Changes\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Settings; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5F,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCyB,WAAW,EAAE,UAAU;IACvBC,KAAK,EAAE,oBAAoB;IAC3BC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1Bb,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMe,UAAU,GAAGb,SAAS,GAAG,cAAc,GAAG,eAAe;EAE/D,MAAMc,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/Cf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,YAAY,GAAIP,CAAC,IAAK;IAC1BA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAErB,QAAQ,CAAC;EAC1C,CAAC;EAED,oBACET,OAAA;IAAK+B,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtChC,OAAA,CAACb,YAAY;MAAC8C,MAAM,EAAE9B,aAAc;MAAC+B,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjItC,OAAA,CAACZ,WAAW;MAAC6B,aAAa,EAAEA,aAAc;MAACZ,SAAS,EAAEA;IAAU;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnEtC,OAAA;MAAM+B,SAAS,EAAE,GAAGb,UAAU,oCAAqC;MAAAc,QAAA,eACjEhC,OAAA;QAAK+B,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEnChC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAI+B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEtC,OAAA;YAAG+B,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eAGNtC,OAAA;UAAK+B,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DhC,OAAA;YAAK+B,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvChC,OAAA;cAAK+B,SAAS,EAAC,mEAAmE;cAAC,cAAW,MAAM;cAAAC,QAAA,gBAClGhC,OAAA;gBACEuC,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,SAAS,CAAE;gBACvCuB,SAAS,EAAE,8DACTxB,SAAS,KAAK,SAAS,GACnB,iCAAiC,GACjC,4EAA4E,EAC/E;gBAAAyB,QAAA,EACJ;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtC,OAAA;gBACEuC,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,eAAe,CAAE;gBAC7CuB,SAAS,EAAE,8DACTxB,SAAS,KAAK,eAAe,GACzB,iCAAiC,GACjC,4EAA4E,EAC/E;gBAAAyB,QAAA,EACJ;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtC,OAAA;gBACEuC,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,UAAU,CAAE;gBACxCuB,SAAS,EAAE,8DACTxB,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,4EAA4E,EAC/E;gBAAAyB,QAAA,EACJ;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtC,OAAA;gBACEuC,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,SAAS,CAAE;gBACvCuB,SAAS,EAAE,8DACTxB,SAAS,KAAK,SAAS,GACnB,iCAAiC,GACjC,4EAA4E,EAC/E;gBAAAyB,QAAA,EACJ;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtC,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBhC,OAAA;cAAMwC,QAAQ,EAAEb,YAAa;cAAAK,QAAA,GAC1BzB,SAAS,KAAK,SAAS,iBACtBP,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBhC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAI+B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1EtC,OAAA;oBAAG+B,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA0C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACNtC,OAAA;kBAAK+B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDhC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,MAAM;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtFtC,OAAA;sBACEuB,IAAI,EAAC,MAAM;sBACXmB,EAAE,EAAC,MAAM;sBACTX,SAAS,EAAC,+GAA+G;sBACzHY,YAAY,EAAC;oBAAY;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNtC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,OAAO;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxFtC,OAAA;sBACEuB,IAAI,EAAC,OAAO;sBACZmB,EAAE,EAAC,OAAO;sBACVX,SAAS,EAAC,+GAA+G;sBACzHY,YAAY,EAAC;oBAAoB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA/B,SAAS,KAAK,eAAe,iBAC5BP,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBhC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAI+B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/EtC,OAAA;oBAAG+B,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAqC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACNtC,OAAA;kBAAK+B,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBhC,OAAA;oBAAK+B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDhC,OAAA;sBAAAgC,QAAA,gBACEhC,OAAA;wBAAI+B,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1EtC,OAAA;wBAAG+B,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAA+B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC,eACNtC,OAAA;sBACEuB,IAAI,EAAC,QAAQ;sBACbQ,SAAS,EAAC,sOAAsO;sBAAAC,QAAA,eAEhPhC,OAAA;wBAAM+B,SAAS,EAAC;sBAAsH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtC,OAAA;oBAAK+B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDhC,OAAA;sBAAAgC,QAAA,gBACEhC,OAAA;wBAAI+B,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzEtC,OAAA;wBAAG+B,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAA0B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC,eACNtC,OAAA;sBACEuB,IAAI,EAAC,QAAQ;sBACbQ,SAAS,EAAC,qOAAqO;sBAAAC,QAAA,eAE/OhC,OAAA;wBAAM+B,SAAS,EAAC;sBAAsH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA/B,SAAS,KAAK,UAAU,iBACvBP,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBhC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAI+B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxEtC,OAAA;oBAAG+B,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAsC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACNtC,OAAA;kBAAK+B,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBhC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,kBAAkB;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC9GtC,OAAA;sBACEuB,IAAI,EAAC,UAAU;sBACfmB,EAAE,EAAC,kBAAkB;sBACrBX,SAAS,EAAC;oBAA+G;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1H,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNtC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,cAAc;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtGtC,OAAA;sBACEuB,IAAI,EAAC,UAAU;sBACfmB,EAAE,EAAC,cAAc;sBACjBX,SAAS,EAAC;oBAA+G;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1H,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNtC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,kBAAkB;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClHtC,OAAA;sBACEuB,IAAI,EAAC,UAAU;sBACfmB,EAAE,EAAC,kBAAkB;sBACrBX,SAAS,EAAC;oBAA+G;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1H,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA/B,SAAS,KAAK,SAAS,iBACtBP,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBhC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAI+B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1EtC,OAAA;oBAAG+B,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAiD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACNtC,OAAA;kBAAK+B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDhC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,aAAa;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpGtC,OAAA;sBACEuB,IAAI,EAAC,MAAM;sBACXmB,EAAE,EAAC,aAAa;sBAChBX,SAAS,EAAC,+GAA+G;sBACzHa,WAAW,EAAC;oBAAqB;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNtC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAOyC,OAAO,EAAC,QAAQ;sBAACV,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/FtC,OAAA;sBACEuB,IAAI,EAAC,MAAM;sBACXmB,EAAE,EAAC,QAAQ;sBACXX,SAAS,EAAC,+GAA+G;sBACzHa,WAAW,EAAC;oBAAO;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGDtC,OAAA;gBAAK+B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpChC,OAAA;kBACEuB,IAAI,EAAC,QAAQ;kBACbQ,SAAS,EAAC,6NAA6N;kBAAAC,QAAA,EACxO;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpC,EAAA,CAlPID,QAAQ;AAAA4C,EAAA,GAAR5C,QAAQ;AAoPd,eAAeA,QAAQ;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}