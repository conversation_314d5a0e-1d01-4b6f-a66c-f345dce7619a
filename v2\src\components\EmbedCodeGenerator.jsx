import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Co<PERSON>, Check, Code, Globe, Smartphone, Monitor } from 'lucide-react';

const EmbedCodeGenerator = ({ isOpen, onClose, clientData }) => {
  const [copied, setCopied] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [buttonStyle, setButtonStyle] = useState('default');
  const [buttonSize, setButtonSize] = useState('medium');

  const generateEmbedCode = () => {
    const baseUrl = 'https://viatryon.com/widget';
    const clientId = clientData?.id || 'demo';
    
    return `<!-- ViaTryon Virtual Try-On Widget -->
<div id="viatryon-widget" data-client-id="${clientId}" data-product-id="${selectedProduct}" data-style="${buttonStyle}" data-size="${buttonSize}"></div>
<script src="${baseUrl}/embed.js" async></script>

<!-- Alternative: Direct Button Integration -->
<button 
  onclick="ViaTryon.open('${selectedProduct}', '${clientId}')"
  class="viatryon-btn viatryon-btn-${buttonStyle} viatryon-btn-${buttonSize}"
>
  Try On Virtually
</button>`;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generateEmbedCode());
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Virtual Try-On Integration</h2>
              <p className="text-gray-600">Generate embed code for your website</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Configuration Panel */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Product ID (optional)
                    </label>
                    <input
                      type="text"
                      value={selectedProduct}
                      onChange={(e) => setSelectedProduct(e.target.value)}
                      placeholder="e.g., watch-model-a"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Leave empty to show product selector
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Style
                    </label>
                    <select
                      value={buttonStyle}
                      onChange={(e) => setButtonStyle(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    >
                      <option value="default">Default</option>
                      <option value="primary">Primary</option>
                      <option value="outline">Outline</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Size
                    </label>
                    <select
                      value={buttonSize}
                      onChange={(e) => setButtonSize(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    >
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                      <option value="large">Large</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Preview */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-center">
                    <button
                      className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                        buttonStyle === 'primary' ? 'bg-[#2D8C88] text-white hover:bg-[#236b68]' :
                        buttonStyle === 'outline' ? 'border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white' :
                        buttonStyle === 'minimal' ? 'text-[#2D8C88] hover:bg-[#2D8C88]/10' :
                        'bg-gray-800 text-white hover:bg-gray-700'
                      } ${
                        buttonSize === 'small' ? 'text-sm px-3 py-1.5' :
                        buttonSize === 'large' ? 'text-lg px-6 py-3' :
                        'text-base px-4 py-2'
                      }`}
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      Try On Virtually
                    </button>
                  </div>
                </div>
              </div>

              {/* Device Compatibility */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Device Compatibility</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Smartphone className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">Mobile</p>
                    <p className="text-xs text-green-600">Optimized</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Monitor className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">Desktop</p>
                    <p className="text-xs text-green-600">Supported</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Globe className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">Tablet</p>
                    <p className="text-xs text-green-600">Supported</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Code Panel */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Embed Code</h3>
                <button
                  onClick={copyToClipboard}
                  className="inline-flex items-center px-3 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors"
                >
                  {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                  {copied ? 'Copied!' : 'Copy Code'}
                </button>
              </div>
              
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm">
                  <code>{generateEmbedCode()}</code>
                </pre>
              </div>

              <div className="mt-6 space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Integration Steps:</h4>
                  <ol className="text-sm text-blue-800 space-y-1">
                    <li>1. Copy the embed code above</li>
                    <li>2. Paste it into your product page HTML</li>
                    <li>3. Replace product-id with your actual product IDs</li>
                    <li>4. Test the integration on your website</li>
                  </ol>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-900 mb-2">Need Help?</h4>
                  <p className="text-sm text-yellow-800">
                    Check our <a href="#" className="underline">integration guide</a> or 
                    <a href="#" className="underline ml-1">contact support</a> for assistance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default EmbedCodeGenerator;
