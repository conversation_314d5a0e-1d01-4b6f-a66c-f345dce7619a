import React, { useState } from 'react';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminNavbar from '../../components/admin/AdminNavbar';
import { motion } from 'framer-motion';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, Users, Eye, ShoppingCart, Clock, Globe, Smartphone, Monitor, Filter } from 'lucide-react';

const TryOnAnalytics = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedClient, setSelectedClient] = useState('all');

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  const metrics = [
    {
      title: 'Total Try-Ons',
      value: '8,956',
      change: '+23%',
      trend: 'up',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      ),
    },
    {
      title: 'Average Duration',
      value: '2m 45s',
      change: '+12%',
      trend: 'up',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      title: 'Conversion Rate',
      value: '24.8%',
      change: '+4%',
      trend: 'up',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
    },
  ];

  const topProducts = [
    {
      name: 'Luxury Watch Model X',
      client: 'Luxury Watches Co.',
      tryOns: 245,
      conversion: '18.5%',
    },
    {
      name: 'Elegant Bracelet Gold',
      client: 'Elegant Bracelets',
      tryOns: 189,
      conversion: '15.2%',
    },
    {
      name: 'Premium Watch Sport',
      client: 'Premium Accessories',
      tryOns: 156,
      conversion: '12.8%',
    },
  ];

  const clientPerformance = [
    {
      name: 'Luxury Watches Co.',
      tryOns: 1245,
      conversion: '22.4%',
      growth: '+15%',
    },
    {
      name: 'Elegant Bracelets',
      tryOns: 956,
      conversion: '18.7%',
      growth: '+12%',
    },
    {
      name: 'Premium Accessories',
      tryOns: 845,
      conversion: '16.2%',
      growth: '+8%',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-20 transition-all duration-300`}>
        <div className="p-4 md:p-6 space-y-6">
          {/* Page Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Virtual Try-On Analytics</h1>
              <p className="text-gray-600">Monitor platform-wide performance and client success metrics</p>
            </div>
            <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
              <div className="inline-flex rounded-lg border border-gray-200 p-1">
                {['7d', '30d', '90d', '1y'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-3 py-1 text-sm font-medium rounded-md ${
                      timeRange === range
                        ? 'bg-[#2D8C88] text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>
              <select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
              >
                <option value="all">All Clients</option>
                <option value="luxury-watches">Luxury Watches Co.</option>
                <option value="elegant-jewelry">Elegant Jewelry</option>
                <option value="fashion-accessories">Fashion Accessories</option>
              </select>
            </div>
          </div>

          {/* Time Range and Client Filter */}
          <div className="mb-6 flex flex-col md:flex-row gap-4">
            <div className="flex space-x-2">
              <button
                onClick={() => setTimeRange('today')}
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  timeRange === 'today'
                    ? 'bg-[#2D8C88] text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                Today
              </button>
              <button
                onClick={() => setTimeRange('week')}
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  timeRange === 'week'
                    ? 'bg-[#2D8C88] text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                This Week
              </button>
              <button
                onClick={() => setTimeRange('month')}
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  timeRange === 'month'
                    ? 'bg-[#2D8C88] text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                This Month
              </button>
            </div>
            <div className="w-full md:w-48">
              <select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
              >
                <option value="all">All Clients</option>
                <option value="luxury watches co.">Luxury Watches Co.</option>
                <option value="elegant bracelets">Elegant Bracelets</option>
                <option value="premium accessories">Premium Accessories</option>
              </select>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6">
            {metrics.map((metric, index) => (
              <motion.div
                key={metric.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-sm p-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                    <p className="text-sm text-green-600 mt-2">
                      <span className="font-medium">{metric.change}</span> from last period
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-[#2D8C88]/10 rounded-lg flex items-center justify-center">
                    {metric.icon}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Charts and Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {/* Try-On Trends Chart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Try-On Trends</h2>
              <div className="h-80 bg-gray-50 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Chart will be displayed here</p>
              </div>
            </motion.div>

            {/* Try-On Distribution Chart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Try-On Distribution</h2>
              <div className="h-80 bg-gray-50 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Chart will be displayed here</p>
              </div>
            </motion.div>
          </div>

          {/* Top Performing Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-xl shadow-sm p-6 mt-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Top Performing Products</h2>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-[#2D8C88]/10 rounded-lg flex items-center justify-center text-[#2D8C88]">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{product.name}</p>
                        <p className="text-xs text-gray-500">{product.client}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-[#2D8C88]">{product.tryOns} try-ons</p>
                        <p className="text-xs text-gray-500">{product.conversion} conversion</p>
                      </div>
                    </div>
                    <div className="mt-1">
                      <div className="h-2 bg-gray-100 rounded-full">
                        <div
                          className="h-2 bg-[#2D8C88] rounded-full"
                          style={{ width: `${(product.tryOns / 245) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Client Performance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white rounded-xl shadow-sm p-6 mt-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Client Performance</h2>
            <div className="space-y-4">
              {clientPerformance.map((client, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-[#2D8C88]/10 rounded-lg flex items-center justify-center text-[#2D8C88]">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{client.name}</p>
                        <p className="text-xs text-gray-500">{client.tryOns} try-ons</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-[#2D8C88]">{client.conversion} conversion</p>
                        <p className="text-xs text-green-600">{client.growth} growth</p>
                      </div>
                    </div>
                    <div className="mt-1">
                      <div className="h-2 bg-gray-100 rounded-full">
                        <div
                          className="h-2 bg-[#2D8C88] rounded-full"
                          style={{ width: `${(client.tryOns / 1245) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </main>
    </div>
  );
};

export default TryOnAnalytics; 