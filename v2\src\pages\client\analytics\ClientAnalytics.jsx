import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import ClientSidebar from '../../../components/client/ClientSidebar';
import ClientNavbar from '../../../components/client/ClientNavbar';
import Overview from './Overview';
import ProductPerformance from './ProductPerformance';
import UserEngagement from './UserEngagement';
import ConversionRates from './ConversionRates';
import TimeAnalysis from './TimeAnalysis';
import DeviceStats from './DeviceStats';
import GeographicData from './GeographicData';

const ClientAnalytics = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const mainMargin = collapsed ? 'md:ml-20' : 'md:ml-72';

  const analyticsTabs = [
    { name: 'Overview', path: '/client/analytics/overview' },
    { name: 'Product Performance', path: '/client/analytics/product-performance' },
    { name: 'User Engagement', path: '/client/analytics/user-engagement' },
    { name: 'Conversion Rates', path: '/client/analytics/conversion-rates' },
    { name: 'Time Analysis', path: '/client/analytics/time-analysis' },
    { name: 'Device Stats', path: '/client/analytics/device-stats' },
    { name: 'Geographic Data', path: '/client/analytics/geographic-data' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <ClientSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        collapsed={collapsed}
        setCollapsed={setCollapsed}
      />
      <div className={`${mainMargin} transition-all duration-300`}>
        <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />
        <main className="pt-20 p-6 space-y-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Analytics Dashboard</h1>
            <p className="mt-1 text-sm text-gray-500">
              Track and analyze your virtual try-on performance
            </p>
          </div>

          {/* Analytics Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {analyticsTabs.map((tab) => (
                <button
                  key={tab.path}
                  onClick={() => navigate(tab.path)}
                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                    location.pathname === tab.path
                      ? 'border-[#2D8C88] text-[#2D8C88]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Analytics Content */}
          <div className="bg-white rounded-lg shadow">
            <Routes>
              <Route path="/" element={<Overview />} />
              <Route path="/overview" element={<Overview />} />
              <Route path="/product-performance" element={<ProductPerformance />} />
              <Route path="/user-engagement" element={<UserEngagement />} />
              <Route path="/conversion-rates" element={<ConversionRates />} />
              <Route path="/time-analysis" element={<TimeAnalysis />} />
              <Route path="/device-stats" element={<DeviceStats />} />
              <Route path="/geographic-data" element={<GeographicData />} />
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ClientAnalytics; 